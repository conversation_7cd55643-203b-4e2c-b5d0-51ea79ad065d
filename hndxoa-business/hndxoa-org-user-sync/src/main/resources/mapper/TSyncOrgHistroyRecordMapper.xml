<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.hndxoa.mapper.TSyncOrgHistroyRecordMapper">

    <select id="selectRecordCount" resultType="java.lang.Integer">
        select count(1) from t_sync_org_histroy_record recors
        left join t_sync_app_system_manage sys on recors.app_id = sys.id
        left join cscp_org org on recors.org_id = org.id
        <where>
            recors.deleted = 0
            <if test="entityDTO.appName != null and entityDTO.appName != ''">
                  and sys.app_name like CONCAT('%', #{entityDTO.appName}, '%')
            </if>
            <if test="entityDTO.appCode != null and entityDTO.appCode != ''">
                  and sys.app_code like CONCAT('%', #{entityDTO.appCode}, '%')
            </if>
            <if test="entityDTO.strOperaType != null and entityDTO.strOperaType != ''">
                 and recors.str_opera_type=#{entityDTO.strOperaType}
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'true'">
                 and recors.sync_success = #{entityDTO.syncSuccess} and (sync_status = '200' or sync_status = '0')
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'false'">
                and (recors.sync_success = #{entityDTO.syncSuccess} or (recors.sync_success = 'true' and sync_status <![CDATA[ <> ]]> '200' and sync_status <![CDATA[ <> ]]> '0'))
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'ing'">
                and recors.sync_success = #{entityDTO.syncSuccess} and sync_status = '205'
            </if>
            <if test="entityDTO.strUnitName != null and entityDTO.strUnitName != ''">
                    and org.org_name like CONCAT('%', #{entityDTO.strUnitName}, '%')
            </if>
            <if test="entityDTO.createTimeStart != null">
                and recors.create_time >= #{entityDTO.createTimeStart}
            </if>
            <if test="entityDTO.createTimeEnd != null">
                and recors.create_time &lt;= #{entityDTO.createTimeEnd}
            </if>
            <if test="entityDTO.sourceId != null">
                and recors.source_id = #{entityDTO.sourceId}
            </if>
        </where>
    </select>

    <select id="selectRecordList" resultType="com.ctsi.hndxoa.entity.dto.TSyncOrgHistroyRecordDTO">
        select sys.app_name,sys.app_code,sys."request_mode",org.org_name as strUnitName,org.str_id,org.str_parent_id,
        recors.* from t_sync_org_histroy_record recors
        left join t_sync_app_system_manage sys on recors.app_id = sys.id
        left join cscp_org org on recors.org_id = org.id
        <where>
            recors.deleted = 0
            <if test="entityDTO.appName != null and entityDTO.appName != ''">
                and sys.app_name like CONCAT('%', #{entityDTO.appName}, '%')
            </if>
            <if test="entityDTO.appCode != null and entityDTO.appCode != ''">
                and sys.app_code like CONCAT('%', #{entityDTO.appCode}, '%')
            </if>
            <if test="entityDTO.strOperaType != null and entityDTO.strOperaType != ''">
                and recors.str_opera_type=#{entityDTO.strOperaType}
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'true'">
                and recors.sync_success = #{entityDTO.syncSuccess} and (sync_status = '200' or sync_status = '0')
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'false'">
                and (recors.sync_success = #{entityDTO.syncSuccess} or (recors.sync_success = 'true' and sync_status <![CDATA[ <> ]]> '200' and sync_status <![CDATA[ <> ]]> '0'))
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'ing'">
                and recors.sync_success = #{entityDTO.syncSuccess} and sync_status = '205'
            </if>
            <if test="entityDTO.strUnitName != null and entityDTO.strUnitName != ''">
                and org.org_name like CONCAT('%', #{entityDTO.strUnitName}, '%')
            </if>
            <if test="entityDTO.createTimeStart != null">
                and recors.create_time >= #{entityDTO.createTimeStart}
            </if>
            <if test="entityDTO.createTimeEnd != null">
                and recors.create_time &lt;= #{entityDTO.createTimeEnd}
            </if>
            <if test="entityDTO.sourceId != null">
                and recors.source_id = #{entityDTO.sourceId}
            </if>
        </where>
        order by recors.create_time desc limit #{startIndex},#{pageSize}
    </select>

    <select id="selectRecordCountNoAdd" resultType="java.lang.Integer">
        select count(1) from t_sync_org_histroy_record recors
        join t_sync_app_system_manage sys on recors.app_id = sys.id
        join cscp_org org on recors.org_id = org.id
        <where>
            recors.deleted = 0
            <if test="entityDTO.appName != null and entityDTO.appName != ''">
                and sys.app_name like CONCAT('%', #{entityDTO.appName}, '%')
            </if>
            <if test="entityDTO.appCode != null and entityDTO.appCode != ''">
                and sys.app_code like CONCAT('%', #{entityDTO.appCode}, '%')
            </if>
            <if test="entityDTO.strOperaType != null and entityDTO.strOperaType != ''">
                and recors.str_opera_type=#{entityDTO.strOperaType}
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'true'">
                and recors.sync_success = #{entityDTO.syncSuccess} and (sync_status = '200' or sync_status = '0')
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'false'">
                and (recors.sync_success = #{entityDTO.syncSuccess} or (recors.sync_success = 'true' and sync_status <![CDATA[ <> ]]> '200' and sync_status <![CDATA[ <> ]]> '0'))
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'ing'">
                and recors.sync_success = #{entityDTO.syncSuccess} and sync_status = '205'
            </if>
            <if test="entityDTO.strUnitName != null and entityDTO.strUnitName != ''">
                and org.org_name like CONCAT('%', #{entityDTO.strUnitName}, '%')
            </if>
            <if test="entityDTO.createBy != null and entityDTO.createBy != ''">
                and recors.create_by = #{entityDTO.createBy}
            </if>
            <if test="entityDTO.createTimeStart != null">
                and recors.create_time >= #{entityDTO.createTimeStart}
            </if>
            <if test="entityDTO.createTimeEnd != null">
                and recors.create_time &lt;= #{entityDTO.createTimeEnd}
            </if>
            <if test="entityDTO.sourceId != null">
                and recors.source_id = #{entityDTO.sourceId}
            </if>
        </where>
    </select>

    <select id="selectRecordListNoAdd" resultType="com.ctsi.hndxoa.entity.dto.TSyncOrgHistroyRecordDTO">
        select sys.app_name,sys.app_code,org.org_name as strUnitName,org.str_id,org.str_parent_id,
        recors.* from t_sync_org_histroy_record recors
        join t_sync_app_system_manage sys on recors.app_id = sys.id
        join cscp_org org on recors.org_id = org.id
        <where>
            recors.deleted = 0
            <if test="entityDTO.appName != null and entityDTO.appName != ''">
                and sys.app_name like CONCAT('%', #{entityDTO.appName}, '%')
            </if>
            <if test="entityDTO.appCode != null and entityDTO.appCode != ''">
                and sys.app_code like CONCAT('%', #{entityDTO.appCode}, '%')
            </if>
            <if test="entityDTO.strOperaType != null and entityDTO.strOperaType != ''">
                and recors.str_opera_type=#{entityDTO.strOperaType}
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'true'">
                and recors.sync_success = #{entityDTO.syncSuccess} and (sync_status = '200' or sync_status = '0')
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'false'">
                and (recors.sync_success = #{entityDTO.syncSuccess} or (recors.sync_success = 'true' and sync_status <![CDATA[ <> ]]> '200' and sync_status <![CDATA[ <> ]]> '0'))
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'ing'">
                and recors.sync_success = #{entityDTO.syncSuccess} and sync_status = '205'
            </if>
            <if test="entityDTO.strUnitName != null and entityDTO.strUnitName != ''">
                and org.org_name like CONCAT('%', #{entityDTO.strUnitName}, '%')
            </if>
            <if test="entityDTO.createBy != null and entityDTO.createBy != ''">
                and recors.create_by = #{entityDTO.createBy}
            </if>
            <if test="entityDTO.createTimeStart != null">
                and recors.create_time >= #{entityDTO.createTimeStart}
            </if>
            <if test="entityDTO.createTimeEnd != null">
                and recors.create_time &lt;= #{entityDTO.createTimeEnd}
            </if>
            <if test="entityDTO.sourceId != null">
                and recors.source_id = #{entityDTO.sourceId}
            </if>
        </where>
        order by recors.create_time desc limit #{startIndex},#{pageSize}
    </select>

    <!--查询需要同步的组织机构列表：根据当前组织机构，查询所有的上级组织机构。根据组织机构当前节点查询所有的父级节点，列表展示-->
    <select id="selectParentSyncOrgList" resultType="com.ctsi.hndxoa.entity.dto.SyncWestoneOrgDTO">
        select t.id, t.type, t.org_name AS orgName, t.parent_id AS parentId,
               t.WESTONE_ORG_ID AS westoneOrgId, t.WESTONE_ORG_PARENT_ID AS westoneOrgParentId,
               t.org_code AS orgCode, t.region_code AS regionCode, t.deleted
        from cscp_org t
        start with t.id = #{entityDTO.orgId} connect by prior t.parent_id = t.id
    </select>

    <!--查询需要同步的组织机构列表：根据当前组织机构，查询所有的子级组织机构。根据组织机构当前节点查询所有的子级节点，列表展示-->
    <select id="selectChildrenSyncOrgList" resultType="com.ctsi.hndxoa.entity.dto.SyncWestoneOrgDTO">
        select t.id, t.type, t.org_name AS orgName, t.parent_id AS parentId,
               t.WESTONE_ORG_ID AS westoneOrgId, t.WESTONE_ORG_PARENT_ID AS westoneOrgParentId,
               t.org_code AS orgCode, t.region_code AS regionCode, t.deleted
        from cscp_org t
        WHERE t.id <![CDATA[ <> ]]> #{entityDTO.orgId}
        start with t.id = #{entityDTO.orgId} connect by prior t.id = t.parent_id
    </select>

    <!--查询需要同步的组织机构列表：查询所有的顶级组织机构，列表展示-->
    <select id="selectRootSyncOrgList" resultType="com.ctsi.hndxoa.entity.dto.SyncWestoneOrgDTO">
        select t.id, t.type, t.org_name AS orgName, t.parent_id AS parentId,
               t.WESTONE_ORG_ID AS westoneOrgId, t.WESTONE_ORG_PARENT_ID AS westoneOrgParentId,
               t.org_code AS orgCode, t.region_code AS regionCode, t.deleted
        from cscp_org t
        WHERE t.parent_id = 0
    </select>

    <!--分页查询所有的组织机构列表-->
    <select id="selectAllSyncOrgList" resultType="com.ctsi.hndxoa.entity.dto.SyncWestoneOrgDTO">
        select t.id, t.type, t.org_name AS orgName, t.parent_id AS parentId,
               t.WESTONE_ORG_ID AS westoneOrgId, t.WESTONE_ORG_PARENT_ID AS westoneOrgParentId,
               t.org_code AS orgCode, t.region_code AS regionCode, t.deleted
        from cscp_org t
        order by t.parent_id ASC
        limit #{startIndex},#{pageSize}
    </select>

    <!--分页查询所有未推送的组织机构列表-->
    <select id="selectUnPushedSyncOrgList" resultType="com.ctsi.hndxoa.entity.dto.SyncWestoneOrgDTO">
        select t.id, t.type, t.org_name AS orgName, t.parent_id AS parentId,
               t.WESTONE_ORG_ID AS westoneOrgId, t.WESTONE_ORG_PARENT_ID AS westoneOrgParentId,
               t.org_code AS orgCode, t.region_code AS regionCode, t.deleted
        from cscp_org t
        where t.WESTONE_ORG_ID IS NULL
        limit #{startIndex},#{pageSize}
    </select>

    <!--查询所有未推送的组织机构数量-->
    <select id="selectUnPushedSyncOrgCount" resultType="java.lang.Integer">
        select count(1)
        from cscp_org t
        where t.WESTONE_ORG_ID IS NULL
    </select>
</mapper>
