<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.hndxoa.mapper.TSyncAppSystemManageMapper">

    <select id="selectUserRoleIds" resultType="java.lang.Long">
        SELECT ur.role_id
        FROM cscp_user_role ur
        WHERE ur.user_id = #{userId}
        AND (ur.role_id IN (
        <foreach collection="roleIds" item="roleId" separator=",">
            #{roleId}
        </foreach>
        ) OR role_id IS NULL)
        AND ur.deleted = 0
    </select>

    <!-- 根据角色ID查询人员ID列表 -->
    <select id="selectUserIdsByRoleId" resultType="java.lang.Long">
        SELECT user_id
        FROM cscp_user_role
        WHERE role_id = #{roleId}
        AND deleted = 0
    </select>

    <select id="unitAdminQueryAppPage" resultType="com.ctsi.hndxoa.entity.TSyncAppSystemManage">
        select distinct app.*
        from "t_sync_app_system_manage" app
        left join "t_sync_app_system_manage_company" app_c
            on app.id = app_c."app_id"
        <where>
            app."deleted" = 0
            <if test="app.appName != null and app.appName != ''">
                and app.app_name like concat('%',#{app.appName},'%')
            </if>
            <if test="app.appCode != null and app.appCode != ''">
                and app.app_code like concat('%',#{app.appCode},'%')
            </if>
            <if test="app.syncUrl != null and app.syncUrl != ''">
                and app.sync_url like concat('%',#{app.syncUrl},'%')
            </if>
            <if test="app.appId != null and app.appId != ''">
                and app.app_id like concat('%',#{app.appId},'%')
            </if>
            <if test="app.appKey != null and app.appKey != ''">
                and app.app_key like concat('%',#{app.appKey},'%')
            </if>
            <if test="app.status != null">
                and app.status = #{app.status}
            </if>
            <if test="app.authentication != null">
                and app.authentication = #{app.authentication}
            </if>
            <if test="app.inSystemFlag != null">
                and app.in_system_flag = #{app.inSystemFlag}
            </if>
            <if test="app.requestMode != null and app.requestMode != ''">
                and app.request_mode = #{app.requestMode}
            </if>
            <if test="app.companyId != null">
                and (app."auto_push" = '1'
                or (app_c."org_id" = #{app.companyId} and app_c."deleted" = 0))
            </if>
        </where>
        order by app."create_time" desc
    </select>

    <select id="selectByManageCompanyAppIds" resultType="com.ctsi.hndxoa.entity.TSyncAppSystemManageCompany">
        SELECT *
        FROM t_sync_app_system_manage_company
        WHERE deleted = 0
        AND app_id IN
        <foreach collection="appIds" item="appId" open="(" separator="," close=")">
            #{appId}
        </foreach>
    </select>

</mapper>
