<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.hndxoa.mapper.TSyncAppModeratorManageMapper">

    <select id="selectAppIdsByUserIdAndCompanyId" resultType="java.lang.Long">
        SELECT DISTINCT app_id FROM (
        SELECT t1.app_id
        FROM t_sync_app_moderator_manage t1
        WHERE deleted=0
        <if test="userId != null">
            AND t1.moderator_id = #{userId}
        </if>

        UNION

        SELECT t2.app_id
        FROM t_sync_app_system_manage_company t2
        WHERE t2.org_id = #{companyId} and deleted=0
        ) combined_result
    </select>
</mapper>
