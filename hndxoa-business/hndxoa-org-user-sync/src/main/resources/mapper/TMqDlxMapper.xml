<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.hndxoa.mapper.TMqDlxMapper">
    <delete id="wlDelete">
        delete from t_mq_dlx where id = #{id}
    </delete>
    <delete id="batchDelete">
        delete from t_mq_dlx where id in
        <foreach item="item" collection="ids" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="pageTMqDlx" resultType="com.ctsi.hndxoa.entity.dto.TMqDlx">
        select id,
               "business_queue",
               "message_id",
               "message_body",
               "fail_reason",
               "retry_count",
               "status",
               "create_time"
        from t_mq_dlx
        <where>
            <if test="tMqDlx.id != null">
                and id = #{tMqDlx.id}
            </if>
            <if test="tMqDlx.businessQueue != null and tMqDlx.businessQueue != ''">
                and "business_queue" like concat('%',#{tMqDlx.businessQueue},'%')
            </if>
            <if test="tMqDlx.messageId != null">
                and "message_id" = #{tMqDlx.messageId}
            </if>
            <if test="tMqDlx.messageBody != null and tMqDlx.messageBody != ''">
                and "message_body" like concat('%',#{tMqDlx.messageBody},'%')
            </if>
            <if test="tMqDlx.failReason != null and tMqDlx.failReason != ''">
                and "fail_reason" like concat('%',#{tMqDlx.failReason},'%')
            </if>
            <if test="tMqDlx.retryCount != null">
                and "retry_count" = #{tMqDlx.retryCount}
            </if>
            <if test="tMqDlx.status != null">
                and "status" = #{tMqDlx.status}
            </if>
            <if test="tMqDlx.createTimeStart != null">
                and "create_time" >= #{tMqDlx.createTimeStart}
            </if>
            <if test="tMqDlx.createTimeEnd != null">
                and "create_time" &lt;= #{tMqDlx.createTimeEnd}
            </if>
        </where>
    </select>
</mapper>
