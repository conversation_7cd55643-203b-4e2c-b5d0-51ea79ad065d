<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.hndxoa.mapper.TSyncAppSystemManageCompanyMapper">

    <select id="getListByAppId" resultType="java.lang.Long">
        select DISTINCT org_id from t_sync_app_system_manage_company
        where deleted = 0 and app_id = #{appId}
        <if test="orgId != '' and orgId != null">
            and org_id in (
            select id from cscp_org
            where deleted = 0 and type = 2
            start with id = #{orgId}
            connect by prior id = parent_id
            )
        </if>
    </select>

    <select id="selectExistingOrgAppPairs" resultType="com.ctsi.hndxoa.entity.TSyncAppSystemManageCompany">
        SELECT * FROM t_sync_app_system_manage_company
        WHERE deleted = 0 and (org_id, app_id) IN
        <foreach collection="dataList" item="pair" open="(" separator="," close=")">
            (#{pair.orgId}, #{pair.appId})
        </foreach>
    </select>

    <insert id="batchInsert">
        INSERT INTO t_sync_app_system_manage_company (id,app_id, app_name, app_code, org_id, org_name,
        create_by,create_name,create_time,company_id,tenant_id,department_id)
        VALUES
        <foreach collection="dataList" item="item" separator=",">
            (#{item.id},#{item.appId}, #{item.appName}, #{item.appCode}, #{item.orgId}, #{item.orgName},
             #{item.createBy},#{item.createName},#{item.createTime},
             #{item.companyId},#{item.tenantId},#{item.departmentId})
        </foreach>
    </insert>
</mapper>
