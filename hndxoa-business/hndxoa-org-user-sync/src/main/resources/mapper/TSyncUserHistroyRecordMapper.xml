<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ctsi.hndxoa.mapper.TSyncUserHistroyRecordMapper">

    <select id="selectRecordCount" resultType="java.lang.Integer">
        select count(1) from t_sync_user_histroy_record recors
        left join t_sync_app_system_manage sys on recors.app_id = sys.id
        left join cscp_user users on recors.user_id = users.id
        <where>
            recors.deleted = 0
            <if test="entityDTO.appName != null and entityDTO.appName != ''">
                and sys.app_name like CONCAT('%', #{entityDTO.appName}, '%')
            </if>
            <if test="entityDTO.appCode != null and entityDTO.appCode != ''">
                and sys.app_code like CONCAT('%', #{entityDTO.appCode}, '%')
            </if>
            <if test="entityDTO.strOperaType != null and entityDTO.strOperaType != ''">
                and recors.str_opera_type=#{entityDTO.strOperaType}
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'true'">
                and recors.sync_success = #{entityDTO.syncSuccess} and (sync_status = '200' or sync_status = '0')
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'ing'">
                and recors.sync_success = #{entityDTO.syncSuccess} and sync_status = '205'
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'false'">
                and (recors.sync_success = #{entityDTO.syncSuccess} or (recors.sync_success = 'true' and sync_status <![CDATA[ <> ]]> '200' and sync_status <![CDATA[ <> ]]> '0'))
            </if>
            <if test="entityDTO.loginName != null and entityDTO.loginName != ''">
                and users.login_name like CONCAT('%', #{entityDTO.loginName}, '%')
            </if>
            <if test="entityDTO.strCname != null and entityDTO.strCname != ''">
                and recors.str_cname like CONCAT('%', #{entityDTO.strCname}, '%')
            </if>
            <if test="entityDTO.strOperaType != null and entityDTO.strOperaType != ''">
                and recors.str_opera_type=#{entityDTO.strOperaType}
            </if>
            <if test="entityDTO.strMobile != null and entityDTO.strMobile != ''">
                and recors.str_mobile like CONCAT('%', #{entityDTO.strMobile}, '%')
            </if>
            <if test="entityDTO.createTimeStart != null">
                and recors.create_time >= #{entityDTO.createTimeStart}
            </if>
            <if test="entityDTO.createTimeEnd != null">
                and recors.create_time &lt;= #{entityDTO.createTimeEnd}
            </if>
        </where>
    </select>

    <select id="selectRecordList" resultType="com.ctsi.hndxoa.entity.dto.TSyncUserHistroyRecordDTO">
        select users.login_name,sys.app_code,sys.app_name,recors.* from t_sync_user_histroy_record recors
        left join t_sync_app_system_manage sys on recors.app_id = sys.id
        left join cscp_user users on recors.user_id = users.id
        <where>
            recors.deleted = 0
            <if test="entityDTO.appName != null and entityDTO.appName != ''">
                and sys.app_name like CONCAT('%', #{entityDTO.appName}, '%')
            </if>
            <if test="entityDTO.appCode != null and entityDTO.appCode != ''">
                and sys.app_code like CONCAT('%', #{entityDTO.appCode}, '%')
            </if>
            <if test="entityDTO.strOperaType != null and entityDTO.strOperaType != ''">
                and recors.str_opera_type=#{entityDTO.strOperaType}
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'true'">
                and recors.sync_success = #{entityDTO.syncSuccess} and (sync_status = '200' or sync_status = '0')
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'ing'">
                and recors.sync_success = #{entityDTO.syncSuccess} and sync_status = '205'
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'false'">
                and (recors.sync_success = #{entityDTO.syncSuccess} or (recors.sync_success = 'true' and sync_status <![CDATA[ <> ]]> '200' and sync_status <![CDATA[ <> ]]> '0'))
            </if>
            <if test="entityDTO.loginName != null and entityDTO.loginName != ''">
                and users.login_name like CONCAT('%', #{entityDTO.loginName}, '%')
            </if>
            <if test="entityDTO.strCname != null and entityDTO.strCname != ''">
                and recors.str_cname like CONCAT('%', #{entityDTO.strCname}, '%')
            </if>
            <if test="entityDTO.strOperaType != null and entityDTO.strOperaType != ''">
                and recors.str_opera_type=#{entityDTO.strOperaType}
            </if>
            <if test="entityDTO.strMobile != null and entityDTO.strMobile != ''">
                and recors.str_mobile like CONCAT('%', #{entityDTO.strMobile}, '%')
            </if>
            <if test="entityDTO.createTimeStart != null">
                and recors.create_time >= #{entityDTO.createTimeStart}
            </if>
            <if test="entityDTO.createTimeEnd != null">
                and recors.create_time &lt;= #{entityDTO.createTimeEnd}
            </if>
        </where>
        order by recors.create_time desc limit #{startIndex},#{pageSize}
    </select>

    <select id="selectRecordCountNoAdd" resultType="java.lang.Integer">
        select count(1) from t_sync_user_histroy_record recors
        join t_sync_app_system_manage sys on recors.app_id = sys.id
        join cscp_user users on recors.user_id = users.id
        <where>
            recors.deleted = 0
            <if test="entityDTO.appName != null and entityDTO.appName != ''">
                and sys.app_name like CONCAT('%', #{entityDTO.appName}, '%')
            </if>
            <if test="entityDTO.appCode != null and entityDTO.appCode != ''">
                and sys.app_code like CONCAT('%', #{entityDTO.appCode}, '%')
            </if>
            <if test="entityDTO.strOperaType != null and entityDTO.strOperaType != ''">
                and recors.str_opera_type=#{entityDTO.strOperaType}
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'true'">
                and recors.sync_success = #{entityDTO.syncSuccess} and (sync_status = '200' or sync_status = '0')
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'ing'">
                and recors.sync_success = #{entityDTO.syncSuccess} and sync_status = '205'
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'false'">
                and (recors.sync_success = #{entityDTO.syncSuccess} or (recors.sync_success = 'true' and sync_status <![CDATA[ <> ]]> '200' and sync_status <![CDATA[ <> ]]> '0'))
            </if>
            <if test="entityDTO.loginName != null and entityDTO.loginName != ''">
                and users.login_name like CONCAT('%', #{entityDTO.loginName}, '%')
            </if>
            <if test="entityDTO.strCname != null and entityDTO.strCname != ''">
                and recors.str_cname like CONCAT('%', #{entityDTO.strCname}, '%')
            </if>
            <if test="entityDTO.strOperaType != null and entityDTO.strOperaType != ''">
                and recors.str_opera_type=#{entityDTO.strOperaType}
            </if>
            <if test="entityDTO.strMobile != null and entityDTO.strMobile != ''">
                and recors.str_mobile like CONCAT('%', #{entityDTO.strMobile}, '%')
            </if>
            <if test="entityDTO.createBy != null and entityDTO.createBy != ''">
                and recors.create_by = #{entityDTO.createBy}
            </if>
            <if test="entityDTO.createTimeStart != null">
                and recors.create_time >= #{entityDTO.createTimeStart}
            </if>
            <if test="entityDTO.createTimeEnd != null">
                and recors.create_time &lt;= #{entityDTO.createTimeEnd}
            </if>
        </where>
    </select>

    <select id="selectRecordListNoAdd" resultType="com.ctsi.hndxoa.entity.dto.TSyncUserHistroyRecordDTO">
        select users.login_name,sys.app_code,sys.app_name,recors.* from t_sync_user_histroy_record recors
        join t_sync_app_system_manage sys on recors.app_id = sys.id
        join cscp_user users on recors.user_id = users.id
        <where>
            recors.deleted = 0
            <if test="entityDTO.appName != null and entityDTO.appName != ''">
                and sys.app_name like CONCAT('%', #{entityDTO.appName}, '%')
            </if>
            <if test="entityDTO.appCode != null and entityDTO.appCode != ''">
                and sys.app_code like CONCAT('%', #{entityDTO.appCode}, '%')
            </if>
            <if test="entityDTO.strOperaType != null and entityDTO.strOperaType != ''">
                and recors.str_opera_type=#{entityDTO.strOperaType}
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'true'">
                and recors.sync_success = #{entityDTO.syncSuccess} and (sync_status = '200' or sync_status = '0')
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'ing'">
                and recors.sync_success = #{entityDTO.syncSuccess} and sync_status = '205'
            </if>
            <if test="entityDTO.syncSuccess != null and entityDTO.syncSuccess != '' and entityDTO.syncSuccess == 'false'">
                and (recors.sync_success = #{entityDTO.syncSuccess} or (recors.sync_success = 'true' and sync_status <![CDATA[ <> ]]> '200' and sync_status <![CDATA[ <> ]]> '0'))
            </if>
            <if test="entityDTO.loginName != null and entityDTO.loginName != ''">
                and users.login_name like CONCAT('%', #{entityDTO.loginName}, '%')
            </if>
            <if test="entityDTO.strCname != null and entityDTO.strCname != ''">
                and recors.str_cname like CONCAT('%', #{entityDTO.strCname}, '%')
            </if>
            <if test="entityDTO.strOperaType != null and entityDTO.strOperaType != ''">
                and recors.str_opera_type=#{entityDTO.strOperaType}
            </if>
            <if test="entityDTO.strMobile != null and entityDTO.strMobile != ''">
                and recors.str_mobile like CONCAT('%', #{entityDTO.strMobile}, '%')
            </if>
            <if test="entityDTO.createBy != null and entityDTO.createBy != ''">
                and recors.create_by = #{entityDTO.createBy}
            </if>
            <if test="entityDTO.createTimeStart != null">
                and recors.create_time >= #{entityDTO.createTimeStart}
            </if>
            <if test="entityDTO.createTimeEnd != null">
                and recors.create_time &lt;= #{entityDTO.createTimeEnd}
            </if>
        </where>
        order by recors.create_time desc limit #{startIndex},#{pageSize}
    </select>

    <select id="queryCscpUserList"  resultType="java.lang.Long">
        SELECT DISTINCT
            cu.id
        FROM cscp_user cu
        LEFT JOIN cscp_user_org cuo ON cu.id = cuo.user_id
        LEFT JOIN (
            SELECT id
            FROM cscp_org
            START WITH id = #{orgId}
            CONNECT BY PRIOR id = parent_id
        ) org_hierarchy ON cuo.org_id = org_hierarchy.id
        LEFT JOIN cscp_org co ON org_hierarchy.id = co.id
        WHERE cu.deleted = 0
        AND cuo.deleted = 0
        AND co.deleted = 0
        AND cu.id IS NOT NULL
    </select>

</mapper>
