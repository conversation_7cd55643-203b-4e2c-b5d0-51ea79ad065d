package com.ctsi.hndxoa.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.entity.TSyncAppSystemManageCompany;
import com.ctsi.hndxoa.entity.dto.TSyncAppSystemManageDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 同步应用系统管理表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 */
public interface TSyncAppSystemManageMapper extends MybatisBaseMapper<TSyncAppSystemManage> {


    /**
     * 查询当前用户所属的角色ID列表
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     * @return 当前用户所属的角色ID列表
     */
    @InterceptorIgnore(tenantLine = "true")
    List<Long> selectUserRoleIds(@Param("userId") Long userId, @Param("roleIds") List<Long> roleIds);

    /**
     * 根据角色ID查询人员ID列表
     * @param roleId 角色ID
     * @return 用户ID列表
     */
    List<Long> selectUserIdsByRoleId(@Param("roleId") Long roleId);

    @InterceptorIgnore(tenantLine = "true")
    IPage<TSyncAppSystemManage> unitAdminQueryAppPage(@Param("page") IPage<TSyncAppSystemManage> page, @Param("app") TSyncAppSystemManageDTO entityDTO);

    @InterceptorIgnore(tenantLine = "true")
    List<TSyncAppSystemManageCompany> selectByManageCompanyAppIds(@Param("appIds") List<Long> appIds);
}
