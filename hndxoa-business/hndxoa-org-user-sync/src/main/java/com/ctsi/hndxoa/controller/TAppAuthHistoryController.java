package com.ctsi.hndxoa.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndxoa.entity.TAppAuthHistory;
import com.ctsi.hndxoa.service.ITAppAuthHistoryService;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * <p>
 * 应用授权市县区单位
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tSyncAppSystemManage/appAuth")
@Api(value = "APP授权记录", tags = "APP授权记录接口")
public class TAppAuthHistoryController extends BaseController {

    @Autowired
    private ITAppAuthHistoryService appAuthHistoryService;

    @GetMapping("/pageHistory")
    public ResultVO<PageResult<TAppAuthHistory>> pageHistory(TAppAuthHistory record, BasePageForm basePageForm) {
        PageResult<TAppAuthHistory> result = appAuthHistoryService.pageHistory(record, basePageForm);
        return ResultVO.success(result);
    }


}
