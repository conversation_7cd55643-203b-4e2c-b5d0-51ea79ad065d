package com.ctsi.hndxoa.westone.queue;

/**
 * @Author: lizuolang
 * @Description: 同步类型枚举类
 * @Date 2024/12/26 09:53
 */
public enum SyncType {

    ORG("同步组织机构"), // 表示同步组织机构
    USER("同步用户"),   // 表示同步用户
    USER_UN_PUSHED("未同步用户");   // 表示同步用户

    private final String description;

    SyncType(String description) {
        this.description = description;
    }

    public String getDescription() {
        return description;
    }

}
