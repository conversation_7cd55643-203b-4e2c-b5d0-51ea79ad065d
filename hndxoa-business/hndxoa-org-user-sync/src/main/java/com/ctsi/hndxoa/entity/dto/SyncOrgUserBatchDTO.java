package com.ctsi.hndxoa.entity.dto;

import com.ctsi.ssdc.security.CscpUserDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class SyncOrgUserBatchDTO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("应用Id")
    private Long appId;

    @ApiModelProperty("机构Id列表")
    private List<Long> orgIds;

    @ApiModelProperty("用户Id列表")
    private List<Long> userIds;

    private CscpUserDetail cscpUserDetail;

    private boolean isAutoPushFlag;

    private String flag;
}
