package com.ctsi.hndxoa.westone.queue;

import com.ctsi.hndxoa.entity.dto.SyncOrgUserDTO;
import com.ctsi.hndxoa.service.IWestoneUasManageService;
import lombok.Data;
import lombok.ToString;

/**
 * @Author: lizuolang
 * @Description: 同步组织机构或用户的任务队列实体类
 * @Date 2024/12/26 09:53
 */
@Data
@ToString
public class SyncBatchStock {

    /**
     * ORG-同步组织机构，USER-同步用户
     */
    private SyncType syncType;

    /**
     * 同步组织机构或用户的数据对象
     */
    private SyncOrgUserDTO syncOrgUserDTO;

    private IWestoneUasManageService westoneUasManageService;

    private SyncBatchStock() {}

    public SyncBatchStock(SyncType syncType, SyncOrgUserDTO syncOrgUserDTO, IWestoneUasManageService westoneUasManageService) {
        this.syncType = syncType;
        this.syncOrgUserDTO = syncOrgUserDTO;
        this.westoneUasManageService = westoneUasManageService;
    }

}
