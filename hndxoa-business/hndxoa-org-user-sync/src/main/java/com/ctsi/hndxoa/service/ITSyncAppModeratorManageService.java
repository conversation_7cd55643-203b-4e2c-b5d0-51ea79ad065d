package com.ctsi.hndxoa.service;

import com.ctsi.hndxoa.entity.dto.TSyncAppModeratorManageDTO;
import com.ctsi.hndxoa.entity.TSyncAppModeratorManage;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndxoa.entity.dto.TSyncAppSystemManageCompany.TSyncAppSystemManageCompanyDTO;
import com.ctsi.ssdc.model.PageResult;
import java.util.List;

/**
 * <p>
 * 版主应用管理表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
public interface ITSyncAppModeratorManageService extends SysBaseServiceI<TSyncAppModeratorManage> {


    /**
     * 分页查询
     *
     * @param entityDTO
     * @param page
     * @return
     */
    PageResult<TSyncAppModeratorManageDTO> queryListPage(TSyncAppModeratorManageDTO entityDTO, BasePageForm page);

    /**
     * 获取所有不分页
     *
     * @param entity
     * @return
     */
    List<TSyncAppModeratorManageDTO> queryList(TSyncAppModeratorManageDTO entity);

    /**
     * 查询当前用户的版主应用id列表
     * */
    List<Long> getCurrentModeratorManageAppList(Long userId);

    List<Long> getCurrentModeratorManageAppListByCompany(Long userId);

    /**
     * 根据主键id获取单个对象
     *
     * @param id
     * @return
     */
    TSyncAppModeratorManageDTO findOne(Long id);

    /**
     * admin授权版主独立管理应用
     *
     * @param entity
     * @return
     */
    //TSyncAppModeratorManageDTO assignModerator(TSyncAppSystemManageCompanyDTO entity);


    /**
     * 更新
     *
     * @param entity
     * @return
     */
    int update(TSyncAppModeratorManageDTO entity);

    /**
     * 删除
     *
     * @param id
     * @return
     */
    int delete(Long id);

     /**
     * 是否存在
     *
     * existByTSyncAppModeratorManageId
     * @param code
     * @return
     */
    boolean existByTSyncAppModeratorManageId(Long code);

    /**
    * 批量新增
    *
    * create batch
    * @param dataList
    * @return
    */
    Boolean insertBatch(List<TSyncAppModeratorManageDTO> dataList);


}
