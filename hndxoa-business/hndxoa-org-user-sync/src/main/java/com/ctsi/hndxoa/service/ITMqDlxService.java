package com.ctsi.hndxoa.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndxoa.entity.dto.TMqDlx;
import com.ctsi.ssdc.model.PageResult;
import org.apache.ibatis.annotations.Param;
import org.springframework.amqp.core.Message;

import java.util.List;

/**
 * <p>
 * 统一消息待办表 服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
public interface ITMqDlxService extends SysBaseServiceI<TMqDlx> {

    void saveDlx(Message amqpMessage, String message, String errorMessage);

    PageResult<TMqDlx> pageTMqDlx(@Param("tMqDlx") TMqDlx tMqDlx, BasePageForm basePageForm);

    int delete(Long id);

    void batchDelete(List<Long> ids);
}
