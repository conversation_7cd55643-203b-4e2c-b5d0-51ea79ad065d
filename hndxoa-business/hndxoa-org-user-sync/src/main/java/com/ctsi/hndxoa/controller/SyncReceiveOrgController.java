package com.ctsi.hndxoa.controller;

import com.alibaba.excel.EasyExcel;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndxoa.entity.dto.SyncReceiveOrgDTO;
import com.ctsi.hndxoa.entity.dto.SyncReceiveOrgTree;
import com.ctsi.hndxoa.service.ISyncReceiveOrgService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/sync/receive/org")
@Api(value = "同步应用系统管理表", tags = "同步应用系统管理表接口")
public class SyncReceiveOrgController extends BaseController {


    @Autowired
    private ISyncReceiveOrgService iSyncReceiveOrgService;



    @ApiOperation(value = "同步其他系统-机构导入")
    @PostMapping("/import")
    public ResultVO<?> orgImport(String rootId,MultipartFile file) throws IOException {
        //不分页
        List<SyncReceiveOrgTree> dataList =  EasyExcel.read(file.getInputStream())
                .head(SyncReceiveOrgTree.class).sheet().doReadSync();
        boolean flag = iSyncReceiveOrgService.importReceiveOrg(rootId, dataList);
        if (flag) {
            return ResultVO.success(true);
        } else {
            return ResultVO.error("导入数据失败!");
        }
    }

    @ApiOperation(value = "其他系统-机构新增接口")
    @PostMapping("/saveOrg")
    public ResultVO<?> saveOrg(@RequestBody SyncReceiveOrgDTO data){
        iSyncReceiveOrgService.saveReceiveOrg(data);
        return ResultVO.success(true);
    }
}
