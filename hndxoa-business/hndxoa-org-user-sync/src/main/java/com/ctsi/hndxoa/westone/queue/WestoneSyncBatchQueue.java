package com.ctsi.hndxoa.westone.queue;

import com.ctsi.hndxoa.entity.dto.SyncOrgUserDTO;
import com.ctsi.hndxoa.service.IWestoneUasManageService;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @Author: lizuolang
 * @Description: 一键批量同步队列引擎
 * @Date 2024/12/26 09:53
 */
public class WestoneSyncBatchQueue implements Runnable {

    // 阻塞队列（BlockingQueue）
    // ArrayBlockingQueue：由数组支持的有界队列
    private final static BlockingQueue<SyncBatchStock> queue = new ArrayBlockingQueue<SyncBatchStock>(1024);

    private final static WestoneSyncBatchQueue engine;

    private WestoneSyncBatchQueue() {}

    static {
        engine = new WestoneSyncBatchQueue();
        ExecutorService service = Executors.newSingleThreadExecutor();
        //ExecutorService service = Executors.newCachedThreadPool();
        service.execute(engine);
    }

    public static WestoneSyncBatchQueue getInstance() {
        return engine;
    }

    public void addBlockingQueueData(SyncBatchStock data) {
        try {
            queue.put(data);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * When an object implementing interface <code>Runnable</code> is used
     * to create a thread, starting the thread causes the object's
     * <code>run</code> method to be called in that separately executing
     * thread.
     * <p>
     * The general contract of the method <code>run</code> is that it may
     * take any action whatsoever.
     *
     * @see Thread#run()
     */
    @Override
    public void run() {
        while (true) {
            try {
                SyncBatchStock data = queue.take();
                execute(data);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 执行
     * @param data 数据对象
     */
    private void execute(SyncBatchStock data) {
        IWestoneUasManageService westoneUasManageService = data.getWestoneUasManageService();
        SyncOrgUserDTO syncOrgUserDTO = data.getSyncOrgUserDTO();
        SyncType syncType = data.getSyncType();
        if (SyncType.ORG.equals(syncType)) {
            westoneUasManageService.syncAllOrgToWestoneUasSystem(syncOrgUserDTO);
        }
        else if (SyncType.USER.equals(syncType)) {
            boolean isDefaultOrg = true;
            westoneUasManageService.syncAllUserToWestoneUasSystem(syncOrgUserDTO, isDefaultOrg);
        } else if (SyncType.USER_UN_PUSHED.equals(syncType)) {
            boolean isDefaultOrg = true;
            westoneUasManageService.syncAllUnPushedUserToWestoneUasSystem(syncOrgUserDTO, isDefaultOrg);
        } else {
            // TODO 其他类型同步
            throw new IllegalArgumentException("未知的同步类型");
        }
    }

}
