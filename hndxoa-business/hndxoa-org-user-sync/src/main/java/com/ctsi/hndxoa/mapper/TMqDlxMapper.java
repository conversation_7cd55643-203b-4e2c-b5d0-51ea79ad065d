package com.ctsi.hndxoa.mapper;


import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.hndxoa.entity.dto.TMqDlx;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <p>
 * 死信队列 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
public interface TMqDlxMapper extends MybatisBaseMapper<TMqDlx> {

    @InterceptorIgnore(tenantLine="true")
    IPage<TMqDlx> pageTMqDlx(IPage iPage, @Param("tMqDlx") TMqDlx tMqDlx);

    @InterceptorIgnore(tenantLine="true")
    int wlDelete(@Param("id") Long id);

    @InterceptorIgnore(tenantLine="true")
    void batchDelete(@Param("ids") List<Long> ids);
}
