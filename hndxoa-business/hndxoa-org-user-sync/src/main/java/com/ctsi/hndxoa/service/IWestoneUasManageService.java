package com.ctsi.hndxoa.service;

import com.ctsi.hndx.westoneuas.westone.WestoneUasApiUtil;
import com.ctsi.hndxoa.entity.dto.SyncOrgUserDTO;
import com.ctsi.ssdc.admin.westoneusa.SyncUserHistroyRecordDTO;

/**
 * <p>
 * 卫士通统一身份认证系统；组织机构、用户同步接口 service
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 *
 */
public interface IWestoneUasManageService {

    /**
     * 获取accessToken
     * @return accessToken
     */
    public String getAccessToken(WestoneUasApiUtil uasApiInstance);

    /**
     * 同步组织机构数据
     * @param syncOrgUserDTO
     * @return
     */
    boolean syncOrgToWestoneUasSystem(SyncOrgUserDTO syncOrgUserDTO);

    /**
     * 同步用户数据
     * @param syncOrgUserDTO 用户同步请求参数
     * @param isDefaultOrg 是否为默认组织机构
     * @return 是否同步成功
     */
    boolean syncUserToWestoneUasSystem(SyncOrgUserDTO syncOrgUserDTO, boolean isDefaultOrg);

    /**
     * 一键同步所有组织机构数据
     * @param syncOrgUserDTO
     * @return
     */
    boolean syncAllOrgToWestoneUasSystem(SyncOrgUserDTO syncOrgUserDTO);

    /**
     * 一键同步所有未推送的组织机构数据
     * @param syncOrgUserDTO
     * @return
     */
    boolean syncAllOrgFromChildrenToWestoneUasSystem(SyncOrgUserDTO syncOrgUserDTO);

    /**
     * 一键同步所有同步用户数据
     * @param syncOrgUserDTO 用户同步请求参数
     * @param isDefaultOrg 是否为默认组织机构
     * @return 是否同步成功
     */
    boolean syncAllUserToWestoneUasSystem(SyncOrgUserDTO syncOrgUserDTO, boolean isDefaultOrg, boolean isPushed);

    /**
     * 一键同步所有同步用户数据
     * @param syncOrgUserDTO 用户同步请求参数
     * @param isDefaultOrg 是否为默认组织机构
     * @return 是否同步成功
     */
    boolean syncAllUserToWestoneUasSystem(SyncOrgUserDTO syncOrgUserDTO, boolean isDefaultOrg);

    /**
     * 一键同步所有未同步的用户数据
     * @param syncOrgUserDTO 用户同步请求参数
     * @param isDefaultOrg 是否为默认组织机构
     * @return 是否同步成功
     */
    boolean syncAllUnPushedUserToWestoneUasSystem(SyncOrgUserDTO syncOrgUserDTO, boolean isDefaultOrg);

    /**
     * 添加推送用户日志记录
     * @param syncUserHistroyRecordDTO
     */
    public void addPushUserLog(SyncUserHistroyRecordDTO syncUserHistroyRecordDTO);

}
