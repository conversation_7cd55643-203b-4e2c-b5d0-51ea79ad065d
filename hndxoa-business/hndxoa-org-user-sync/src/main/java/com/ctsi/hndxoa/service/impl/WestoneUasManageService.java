package com.ctsi.hndxoa.service.impl;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.addrbook.service.ITAddressBookService;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.constant.SysConstant;
import com.ctsi.hndx.tsysconfig.service.ISysConfigService;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.hndx.westoneuas.util.trace.EnableTraceWatch;
import com.ctsi.hndx.westoneuas.util.trace.TraceLevel;
import com.ctsi.hndx.westoneuas.westone.WestoneResEnum;
import com.ctsi.hndx.westoneuas.westone.WestoneUasApiUtil;
import com.ctsi.hndx.westoneuas.westone.entity.org.UasOrg;
import com.ctsi.hndx.westoneuas.westone.entity.org.UasOrgOptRes;
import com.ctsi.hndx.westoneuas.westone.entity.org.UasOrgQueryDTO;
import com.ctsi.hndx.westoneuas.westone.entity.org.UasOrgRes;
import com.ctsi.hndx.westoneuas.westone.entity.uas.TokenRes;
import com.ctsi.hndx.westoneuas.westone.entity.user.UasUser;
import com.ctsi.hndx.westoneuas.westone.entity.user.UasUserOptRes;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.entity.TSyncOrgHistroyRecord;
import com.ctsi.hndxoa.entity.TSyncUserHistroyRecord;
import com.ctsi.hndxoa.entity.dto.SyncOrgUserDTO;
import com.ctsi.hndxoa.entity.dto.SyncWestoneOrgDTO;
import com.ctsi.hndxoa.mapper.TSyncAppSystemManageMapper;
import com.ctsi.hndxoa.mapper.TSyncOrgHistroyRecordMapper;
import com.ctsi.hndxoa.mapper.TSyncUserHistroyRecordMapper;
import com.ctsi.hndxoa.service.IWestoneUasManageService;
import com.ctsi.hndxoa.service.constant.OrgUserConstants;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.CscpUserOrg;
import com.ctsi.ssdc.admin.service.CscpOrgService;
import com.ctsi.ssdc.admin.service.CscpUserOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.ssdc.admin.westoneusa.SyncUserHistroyRecordDTO;
import com.ctsi.ssdc.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <p>
 * 卫士通统一身份认证系统；组织机构、用户同步接口 service实现
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 *
 */
@Slf4j
@Service
public class WestoneUasManageService implements IWestoneUasManageService {

    private final static String REDIS_KEY_ACCESS_TOKEN = "xbt:westone_uas:accessToken";

    private final static String REDIS_KEY_XBT_ORG = "xbt:westone_org:";

    public final static String WESTONE_UAS_SYSTEM = "westone_uas_system";

    private ISysConfigService tSysConfigService;

    private RedisUtil redisUtil;

    private CscpOrgService cscpOrgService;

    private CscpUserService cscpUserService;

    private CscpUserOrgService cscpUserOrgService;

    private ITAddressBookService tAddressBookService;

    private TSyncOrgHistroyRecordMapper tSyncOrgHistroyRecordMapper;

    private TSyncUserHistroyRecordMapper tSyncUserHistroyRecordMapper;

    private TSyncAppSystemManageMapper tSyncAppSystemManageMapper;

    public WestoneUasManageService(ISysConfigService tSysConfigService, RedisUtil redisUtil,
                                   CscpOrgService cscpOrgService, CscpUserService cscpUserService,
                                   CscpUserOrgService cscpUserOrgService, ITAddressBookService tAddressBookService,
                                   TSyncOrgHistroyRecordMapper tSyncOrgHistroyRecordMapper,
                                   TSyncUserHistroyRecordMapper tSyncUserHistroyRecordMapper,
                                   TSyncAppSystemManageMapper tSyncAppSystemManageMapper) {
        this.tSysConfigService = tSysConfigService;
        this.redisUtil = redisUtil;
        this.cscpOrgService = cscpOrgService;
        this.cscpUserService = cscpUserService;
        this.cscpUserOrgService = cscpUserOrgService;
        this.tAddressBookService = tAddressBookService;
        this.tSyncOrgHistroyRecordMapper = tSyncOrgHistroyRecordMapper;
        this.tSyncUserHistroyRecordMapper = tSyncUserHistroyRecordMapper;
        this.tSyncAppSystemManageMapper = tSyncAppSystemManageMapper;
    }

    /**
     * 从系统动态参数中获取统一身份认证系统对接的配置信息
     * @return
     */
    @Deprecated
    private WestoneUasApiUtil getUasApiInstance() {
        String baseUrl = this.tSysConfigService.getSysConfigValueByCode("westone.uas.base_url");
        String appId = this.tSysConfigService.getSysConfigValueByCode("westone.uas.app_id");
        String appSecret = this.tSysConfigService.getSysConfigValueByCode("westone.uas.app_secret");
        //String tenantId = this.tSysConfigService.getSysConfigValueByCode("westone.uas.default_tenantId");
        // TODO 不传租户ID
        WestoneUasApiUtil uasApiInstance = WestoneUasApiUtil.getInstance(baseUrl, appId, appSecret, null);
        return uasApiInstance;
    }

    /**
     * 从应用管理中中获取统一身份认证系统对接的配置信息
     * @param appSystemManage
     * @return
     */
    private WestoneUasApiUtil getUasApiInstance(TSyncAppSystemManage appSystemManage) {
        String baseUrl = appSystemManage.getSyncUrl();
        String appId = appSystemManage.getAppId();
        String appSecret = appSystemManage.getAppKey();
        // TODO 不传租户ID
        WestoneUasApiUtil uasApiInstance = WestoneUasApiUtil.getInstance(baseUrl, appId, appSecret, null);
        return uasApiInstance;
    }

    /**
     * 获取accessToken
     *
     * @return accessToken
     */
    @Override
    public String getAccessToken(WestoneUasApiUtil uasApiInstance) {
        String accessToken = null;
        boolean isExpire = false;
        if (!this.redisUtil.hasKey(REDIS_KEY_ACCESS_TOKEN) || Objects.isNull(redisUtil.get(REDIS_KEY_ACCESS_TOKEN))) {
            isExpire = true;
        } else {
            long expireTime = this.redisUtil.getExpire(REDIS_KEY_ACCESS_TOKEN);
            // 过期时长的单位为秒，需要进行转换分钟
            long timeLength = expireTime / 60;
            // 因为双方的时间差，所以提前五分钟进行token的更新
            if (timeLength == 5) {
                // 刷新token
                isExpire = true;
            } else {
                accessToken = (String) this.redisUtil.get(REDIS_KEY_ACCESS_TOKEN);
            }
        }
        if (isExpire) {
            TokenRes uasRes = uasApiInstance.getToken();
            accessToken = uasRes.getAccessToken();
            this.redisUtil.set(REDIS_KEY_ACCESS_TOKEN, accessToken, uasRes.getAccessTokenExpires(), TimeUnit.MINUTES);
        }
        return accessToken;
    }

    /**
     * 同步组织机构数据：新增
     * @param syncOrgUserDTO
     * @param cscpOrgs
     * @return
     */
    public boolean syncAddOrgToWestoneUasSystem(SyncOrgUserDTO syncOrgUserDTO, List<SyncWestoneOrgDTO> cscpOrgs) {
        TSyncAppSystemManage appSystemManage = tSyncAppSystemManageMapper.selectOneNoAdd(
                new LambdaQueryWrapper<TSyncAppSystemManage>().eq(TSyncAppSystemManage::getId, syncOrgUserDTO.getAppId()));
        if (appSystemManage == null) {
            log.error("同步组织机构信息到到卫士通统一身份认证系统syncOrgToWestoneUasSystem，请求参数有误");
            return false;
        }

        UasOrgOptRes errorResp = new UasOrgOptRes();
        errorResp.setCode(WestoneResEnum.FAILURE.getCode());

        WestoneUasApiUtil uasApiInstance = getUasApiInstance(appSystemManage);
        try {
            // TODO 第一次遍历，创建所有的组织机构，并将第三方组织机构ID缓存本地
            for (SyncWestoneOrgDTO syncWestoneOrgDTO : cscpOrgs) {
                if (null == syncWestoneOrgDTO) {
                    continue;
                }
                String orgName = syncWestoneOrgDTO.getOrgName();
                // TODO 兼容错误信息:含有特殊字符，去掉括号 aaa(bbb)改为aaa-bbb
                if (orgName.contains("(")) {
                    orgName = orgName.replace("(", "-").replace(")", "");
                }
                UasOrgQueryDTO orgQueryDTO = new UasOrgQueryDTO();
                orgQueryDTO.setName(orgName);
                orgQueryDTO.setCode(syncWestoneOrgDTO.getOrgCode());
                orgQueryDTO.setOffset(1);
                orgQueryDTO.setLimit(1);
                UasOrgRes uasOrgRes = uasApiInstance.listOrgs(getAccessToken(uasApiInstance), orgQueryDTO);
                if (null != uasOrgRes) {
                    UasOrgOptRes currentUasOrgRes = null;
                    if (null == uasOrgRes.getOrganizations() || uasOrgRes.getOrganizations().size() == 0) {
                        UasOrg currentUasOrg = new UasOrg();
                        currentUasOrg.setCode(syncWestoneOrgDTO.getOrgCode());
                        currentUasOrg.setName(orgName);
//                    currentUasOrg.setType("2");
                        currentUasOrg.setAddress(syncWestoneOrgDTO.getRegionCode());
                        if (redisUtil.hasKey(REDIS_KEY_XBT_ORG + syncWestoneOrgDTO.getId())) {
                            currentUasOrg.setParentId(Objects.isNull(redisUtil.get(REDIS_KEY_XBT_ORG + syncWestoneOrgDTO.getId())) ? null : redisUtil.get(REDIS_KEY_XBT_ORG + syncWestoneOrgDTO.getId()).toString());
                        }

                        log.info("同步组织机构信息到到卫士通统一身份认证系统，新增组织机构，请求参数{}", JSON.toJSONString(currentUasOrg));
                        currentUasOrgRes = uasApiInstance.createOrg(getAccessToken(uasApiInstance), currentUasOrg);

                        // 记录推送日志
                        addPushOrgLog(appSystemManage.getId(), syncWestoneOrgDTO, currentUasOrgRes, WestoneUasApiUtil.OPT_ADD);
                    } else {
                        // 已存在 直接取卫士通组织机构ID
                        if (uasOrgRes.getOrganizations().size() > 0) {
                            UasOrg uasOrg = uasOrgRes.getOrganizations().get(0);
                            UasOrg currentUasOrg = new UasOrg();
                            currentUasOrg.setOrgId(uasOrg.getOrgId());
                            currentUasOrg.setCode(syncWestoneOrgDTO.getOrgCode());
                            currentUasOrg.setName(orgName);
//                    currentUasOrg.setType("2");
                            currentUasOrg.setAddress(syncWestoneOrgDTO.getRegionCode());
                            currentUasOrg.setParentId(uasOrg.getParentId());

                            log.info("同步组织机构信息到到卫士通统一身份认证系统，更新组织机构，请求参数{}", JSON.toJSONString(currentUasOrg));
                            currentUasOrgRes = uasApiInstance.updateOrg(getAccessToken(uasApiInstance), currentUasOrg);

                            // 记录推送日志
                            addPushOrgLog(appSystemManage.getId(), syncWestoneOrgDTO, currentUasOrgRes, WestoneUasApiUtil.OPT_UPDATE);
                        }

                        if (null != currentUasOrgRes.getOrgId()) {
                            // 缓存到本地，方便后续更新
                            redisUtil.set(REDIS_KEY_XBT_ORG + syncWestoneOrgDTO.getId(), currentUasOrgRes.getOrgId());

                            // TODO 更新
                            LambdaUpdateWrapper<CscpOrg> updateWrapper = Wrappers.lambdaUpdate();
                            updateWrapper.eq(CscpOrg::getId, syncWestoneOrgDTO.getId())
                                    .set(CscpOrg::getWestoneOrgId, currentUasOrgRes.getOrgId());
                            cscpOrgService.update(null, updateWrapper);
                        }
                    }
                } else {
                    UasOrg currentUasOrg = new UasOrg();
                    currentUasOrg.setOrgId(syncWestoneOrgDTO.getWestoneOrgId());
                    currentUasOrg.setCode(syncWestoneOrgDTO.getOrgCode());
                    currentUasOrg.setName(orgName);
//                    currentUasOrg.setType("2");
                    currentUasOrg.setAddress(syncWestoneOrgDTO.getRegionCode());
                    if (redisUtil.hasKey(REDIS_KEY_XBT_ORG + syncWestoneOrgDTO.getId())) {
                        currentUasOrg.setParentId(redisUtil.get(REDIS_KEY_XBT_ORG + syncWestoneOrgDTO.getId()).toString());
                    }

                    log.info("同步组织机构信息到到卫士通统一身份认证系统，更新组织机构，请求参数{}", JSON.toJSONString(currentUasOrg));
                    UasOrgOptRes currentUasOrgRes = uasApiInstance.updateOrg(getAccessToken(uasApiInstance), currentUasOrg);

                    // 记录推送日志
                    addPushOrgLog(appSystemManage.getId(), syncWestoneOrgDTO, currentUasOrgRes, WestoneUasApiUtil.OPT_UPDATE);
                }
            }
        } catch (Exception e) {
            errorResp.setMessage(MessageFormat.format("同步组织机构信息到卫士通统一身份认证系统，" +
                            "组织机构[appId={0},orgId={1}]请求接口失败：{2}",
                    syncOrgUserDTO.getAppId(), syncOrgUserDTO.getOrgId(), e.getMessage()));
            log.error(errorResp.getMessage());

            // 记录推送日志
            addPushOrgLog(appSystemManage.getId(), null, errorResp, WestoneUasApiUtil.OPT_UPDATE);
            return false;
        }
        return true;
    }

    /**
     * 同步组织机构数据：更新
     * @param syncOrgUserDTO
     * @param cscpOrgs
     * @return
     */
    public boolean syncUpdateOrgToWestoneUasSystem(SyncOrgUserDTO syncOrgUserDTO, List<SyncWestoneOrgDTO> cscpOrgs) {
        TSyncAppSystemManage appSystemManage = tSyncAppSystemManageMapper.selectOneNoAdd(
                new LambdaQueryWrapper<TSyncAppSystemManage>().eq(TSyncAppSystemManage::getId, syncOrgUserDTO.getAppId()));
        if (appSystemManage == null) {
            log.error("同步组织机构信息到到卫士通统一身份认证系统syncOrgToWestoneUasSystem，请求参数有误");
            return false;
        }

        UasOrgOptRes errorResp = new UasOrgOptRes();
        errorResp.setCode(WestoneResEnum.FAILURE.getCode());

        WestoneUasApiUtil uasApiInstance = getUasApiInstance(appSystemManage);
        try {
            for (SyncWestoneOrgDTO syncWestoneOrgDTO : cscpOrgs) {
                UasOrg currentUasOrg = new UasOrg();
                if (null == syncWestoneOrgDTO.getWestoneOrgId() || "".equals(syncWestoneOrgDTO.getWestoneOrgId())) {
                    UasOrgQueryDTO orgQueryDTO = new UasOrgQueryDTO();
                    String orgName = syncWestoneOrgDTO.getOrgName();
                    // TODO 兼容错误信息:含有特殊字符，去掉括号 aaa(bbb)改为aaa-bbb
                    if (orgName.contains("(")) {
                        orgName = orgName.replace("(", "-").replace(")", "");
                    }
                    orgQueryDTO.setName(orgName);
                    orgQueryDTO.setCode(syncWestoneOrgDTO.getOrgCode());
                    orgQueryDTO.setOffset(1);
                    orgQueryDTO.setLimit(1);
                    UasOrgRes uasOrgRes = uasApiInstance.listOrgs(getAccessToken(uasApiInstance), orgQueryDTO);
                    if (null != uasOrgRes) {
                        if (uasOrgRes.getOrganizations().size() > 0) {
                            currentUasOrg.setOrgId(uasOrgRes.getOrganizations().get(0).getOrgId());
                        }
                    }
                } else {
                    currentUasOrg.setOrgId(syncWestoneOrgDTO.getWestoneOrgId());
                }

                currentUasOrg.setCode(syncWestoneOrgDTO.getOrgCode());
                currentUasOrg.setName(syncWestoneOrgDTO.getOrgName());
//                currentUasOrg.setType("2");
                currentUasOrg.setAddress(syncWestoneOrgDTO.getRegionCode());
                // 从缓存中获取父节点ID
                currentUasOrg.setParentId(redisUtil.get(REDIS_KEY_XBT_ORG + syncWestoneOrgDTO.getParentId()).toString());

                log.info("同步组织机构信息到到卫士通统一身份认证系统，更新组织机构，请求参数{}", JSON.toJSONString(currentUasOrg));
                UasOrgOptRes currentUasOrgRes = uasApiInstance.updateOrg(getAccessToken(uasApiInstance), currentUasOrg);

                // 记录推送日志
                addPushOrgLog(appSystemManage.getId(), syncWestoneOrgDTO, currentUasOrgRes, WestoneUasApiUtil.OPT_UPDATE);

                if (currentUasOrg.getParentId() != null && !"".equals(currentUasOrg.getParentId())) {
                    // TODO 更新
                    LambdaUpdateWrapper<CscpOrg> updateWrapper = Wrappers.lambdaUpdate();
                    updateWrapper.eq(CscpOrg::getId, syncWestoneOrgDTO.getId())
                            .set(CscpOrg::getWestoneOrgParentId, currentUasOrg.getParentId());
                    cscpOrgService.update(null, updateWrapper);
                }
            }
        } catch (Exception e) {
            errorResp.setMessage(MessageFormat.format("同步组织机构信息到卫士通统一身份认证系统，" +
                            "组织机构[appId={0},orgId={1}]请求接口失败：{2}",
                    syncOrgUserDTO.getAppId(), syncOrgUserDTO.getOrgId(), e.getMessage()));
            log.error(errorResp.getMessage());

            // 记录推送日志
            addPushOrgLog(appSystemManage.getId(), null, errorResp, WestoneUasApiUtil.OPT_UPDATE);
            return false;
        }
        return true;
    }

    /**
     * 同步组织机构数据
     * @param syncOrgUserDTO
     * @return
     */
    @EnableTraceWatch(level = TraceLevel.INFO, notes = "同步组织机构信息到卫士通统一身份认证系统")
    @Override
    public boolean syncOrgToWestoneUasSystem(SyncOrgUserDTO syncOrgUserDTO) {
        TSyncAppSystemManage appSystemManage = syncOrgUserDTO.getCompatibleApp(tSyncAppSystemManageMapper);
        if (appSystemManage == null) {
            log.error("同步组织机构信息到到卫士通统一身份认证系统syncOrgToWestoneUasSystem，请求参数有误");
            return false;
        }
        UasOrgOptRes errorResp = new UasOrgOptRes();
        errorResp.setCode(WestoneResEnum.FAILURE.getCode());
        try {
            List<SyncWestoneOrgDTO> cscpOrgs = tSyncOrgHistroyRecordMapper.selectParentSyncOrgList(syncOrgUserDTO);
            // 第一次遍历，创建所有的组织机构，并将第三方组织机构ID缓存到redis
            syncAddOrgToWestoneUasSystem(syncOrgUserDTO, cscpOrgs);
            // 第二次遍历，从redis获取第三方组织机构ID缓存，更新所有节点的第三方组织机构父节点ID
            syncUpdateOrgToWestoneUasSystem(syncOrgUserDTO, cscpOrgs);
        } catch (Exception e) {
            errorResp.setMessage(MessageFormat.format("同步组织机构信息到卫士通统一身份认证系统，" +
                            "组织机构[appId={0},orgId={1}]请求接口失败：{2}",
                    syncOrgUserDTO.getAppId(), syncOrgUserDTO.getOrgId(), e.getMessage()));
            log.error(errorResp.getMessage());

            // 记录推送日志
            addPushOrgLog(appSystemManage.getId(), null, errorResp, WestoneUasApiUtil.OPT_UPDATE);
            return false;
        }
        return true;
    }

    /**
     * 一键同步所有组织机构数据
     *
     * @param syncOrgUserDTO
     * @return
     */
    @EnableTraceWatch(level = TraceLevel.INFO, notes = "一键批量同步所有组织机构信息到卫士通统一身份认证系统")
    @Override
    public boolean syncAllOrgToWestoneUasSystem(SyncOrgUserDTO syncOrgUserDTO) {
        TSyncAppSystemManage appSystemManage = tSyncAppSystemManageMapper.selectOneNoAdd(
                new LambdaQueryWrapper<TSyncAppSystemManage>().eq(TSyncAppSystemManage::getId, syncOrgUserDTO.getAppId()));
        if (appSystemManage == null) {
            log.error("同步组织机构信息到到卫士通统一身份认证系统syncOrgToWestoneUasSystem，请求参数有误");
            return false;
        }

        UasOrgOptRes errorResp = new UasOrgOptRes();
        errorResp.setCode(WestoneResEnum.FAILURE.getCode());

        WestoneUasApiUtil uasApiInstance = getUasApiInstance(appSystemManage);

//        LambdaQueryWrapper<CscpOrg> lambdaQueryWrapper = new LambdaQueryWrapper<>();
//        if (null != syncOrgUserDTO.getUserId() && !"".equals(syncOrgUserDTO.getUserId())) {
//            lambdaQueryWrapper.eq(CscpOrg::getId, syncOrgUserDTO.getOrgId());
//        }
        try {
            List<SyncWestoneOrgDTO> cscpOrgs = tSyncOrgHistroyRecordMapper.selectRootSyncOrgList(syncOrgUserDTO);
            for (SyncWestoneOrgDTO cscpOrg : cscpOrgs) {
                // 根据顶级节点查询所有子节点
                SyncOrgUserDTO childrenSyncOrgUserDTO = new SyncOrgUserDTO();
                childrenSyncOrgUserDTO.setOrgId(cscpOrg.getId());
                List<SyncWestoneOrgDTO> cscpOrgTreeList = tSyncOrgHistroyRecordMapper.selectChildrenSyncOrgList(childrenSyncOrgUserDTO);
                cscpOrgTreeList.add(cscpOrg);
                List<SyncWestoneOrgDTO> orgTrees = getOrgTree(cscpOrgTreeList, 0L);

                for (SyncWestoneOrgDTO orgTree : orgTrees) {
                    traverseAddOrUpdateOrgToWestoneUasSystem(orgTree, 0, appSystemManage.getId(), uasApiInstance, null);
                }
            }
        } catch (Exception e) {
            log.error("同步组织机构信息到卫士通统一身份认证系统[appId={}]请求接口失败：{}",
                    syncOrgUserDTO.getAppId(), e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * 一键同步所有未推送组织机构数据
     *
     * @param syncOrgUserDTO
     * @return
     */
    @EnableTraceWatch(level = TraceLevel.INFO, notes = "一键批量同步所有组织机构信息（从下往上依次遍历）到卫士通统一身份认证系统")
    public boolean syncAllOrgFromChildrenToWestoneUasSystem(SyncOrgUserDTO syncOrgUserDTO) {
        TSyncAppSystemManage appSystemManage = tSyncAppSystemManageMapper.selectOneNoAdd(
                new LambdaQueryWrapper<TSyncAppSystemManage>().eq(TSyncAppSystemManage::getId, syncOrgUserDTO.getAppId()));
        if (appSystemManage == null) {
            log.error("同步组织机构信息到到卫士通统一身份认证系统syncOrgToWestoneUasSystem，请求参数有误");
            return false;
        }
        int pageSize = 50;
        try {
            int totalCount = tSyncOrgHistroyRecordMapper.selectUnPushedSyncOrgCount(syncOrgUserDTO);
            log.info("一键同步所有组织机构数据到卫士通统一身份认证系统，分批处理，总条数：{}，分批处理，每次处理条数：{}", totalCount, pageSize);
            int totalPages = (totalCount + pageSize - 1) / pageSize;
            for (int page = 1; page <= totalPages; page++) {
                int startIndex = (page - 1) * pageSize;
                List<SyncWestoneOrgDTO> orgDTOS = tSyncOrgHistroyRecordMapper.selectUnPushedSyncOrgList(syncOrgUserDTO, startIndex, pageSize);
                log.info("[创建或更新组织机构]一键同步所有组织机构数据到卫士通统一身份认证系统，分批处理，当前批次：{}，处理条数：{}", page, orgDTOS.size());
                for (SyncWestoneOrgDTO orgTree : orgDTOS) {
                    syncOrgUserDTO.setOrgId(orgTree.getId());
                    syncOrgToWestoneUasSystem(syncOrgUserDTO);
                }
            }
        } catch (Exception e) {
            log.error("同步组织机构信息到卫士通统一身份认证系统[appId={}]请求接口失败：{}",
                    syncOrgUserDTO.getAppId(), e.getMessage());
            return false;
        }
        return true;
    }

    public void traverseAddOrUpdateOrgToWestoneUasSystem(SyncWestoneOrgDTO node, int level, Long applicationId,
                             WestoneUasApiUtil uasApiInstance, String westonOrgParentId) {
        if (node == null) {
            return;
        }

        // 打印当前节点，带有缩进表示层级
//        printOrgTree(node, level);

        // 创建或更新上级节点
        UasOrgOptRes currentUasOrgRes = addOrUpdateOrgToWestoneUasSystem(node, applicationId, uasApiInstance, westonOrgParentId);

        // 遍历当前节点的所有子节点
        for (SyncWestoneOrgDTO child : node.getChildren()) {
//            // 创建或更新子级节点
//            UasOrgOptRes childrenOrgRes = addOrUpdateOrgToWestoneUasSystem(child, applicationId, uasApiInstance, currentUasOrgRes.getOrgId());
            // 递归遍历子节点
            traverseAddOrUpdateOrgToWestoneUasSystem(child, level + 1, applicationId, uasApiInstance, currentUasOrgRes.getOrgId());
        }
    }

    private void printOrgTree(SyncWestoneOrgDTO node, int level) {
        for (int i = 0; i < level; i++) {
            System.out.print(" ");
        }
        System.out.print(node.getOrgName() + "--" +  level + "\n");
    }

    private UasOrgOptRes addOrUpdateOrgToWestoneUasSystem(SyncWestoneOrgDTO syncWestoneOrgDTO, Long applicationId,
                                                  WestoneUasApiUtil uasApiInstance, String westonOrgParentId) {
        UasOrgOptRes currentUasOrgRes = null;
        if ((null == syncWestoneOrgDTO.getWestoneOrgId() || "".equals(syncWestoneOrgDTO.getWestoneOrgId()))) {

            UasOrgQueryDTO orgQueryDTO = new UasOrgQueryDTO();
            orgQueryDTO.setName(syncWestoneOrgDTO.getOrgName());
            orgQueryDTO.setCode(syncWestoneOrgDTO.getOrgCode());
            orgQueryDTO.setOffset(1);
            orgQueryDTO.setLimit(1);
            UasOrgRes uasOrgRes = uasApiInstance.listOrgs(getAccessToken(uasApiInstance), orgQueryDTO);
            if (null != uasOrgRes) {
                if (null == uasOrgRes.getOrganizations() || uasOrgRes.getOrganizations().size() == 0) {
                    UasOrg currentUasOrg = new UasOrg();
                    currentUasOrg.setCode(syncWestoneOrgDTO.getOrgCode());
                    currentUasOrg.setName(syncWestoneOrgDTO.getOrgName());
//                    currentUasOrg.setType("2");
                    currentUasOrg.setAddress(syncWestoneOrgDTO.getRegionCode());
                    currentUasOrg.setParentId(westonOrgParentId);

                    log.info("同步组织机构信息到到卫士通统一身份认证系统，新增组织机构，请求参数{}", JSON.toJSONString(currentUasOrg));
                    currentUasOrgRes = uasApiInstance.createOrg(getAccessToken(uasApiInstance), currentUasOrg);

                    // 记录推送日志
                    addPushOrgLog(applicationId, syncWestoneOrgDTO, currentUasOrgRes, WestoneUasApiUtil.OPT_ADD);
                } else {
                    // 已存在 直接取卫士通组织机构ID
                    if (uasOrgRes.getOrganizations().size() > 0) {
                        UasOrg uasOrg = uasOrgRes.getOrganizations().get(0);
                        UasOrg currentUasOrg = new UasOrg();
                        currentUasOrg.setOrgId(uasOrg.getOrgId());
                        currentUasOrg.setCode(syncWestoneOrgDTO.getOrgCode());
                        currentUasOrg.setName(syncWestoneOrgDTO.getOrgName());
//                    currentUasOrg.setType("2");
                        currentUasOrg.setAddress(syncWestoneOrgDTO.getRegionCode());
                        currentUasOrg.setParentId(westonOrgParentId);

                        log.info("同步组织机构信息到到卫士通统一身份认证系统，更新组织机构，请求参数{}", JSON.toJSONString(currentUasOrg));
                        currentUasOrgRes = uasApiInstance.updateOrg(getAccessToken(uasApiInstance), currentUasOrg);

                        // 记录推送日志
                        addPushOrgLog(applicationId, syncWestoneOrgDTO, currentUasOrgRes, WestoneUasApiUtil.OPT_UPDATE);
                    }
                }

                // TODO 更新
                LambdaUpdateWrapper<CscpOrg> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.eq(CscpOrg::getId, syncWestoneOrgDTO.getId())
                        .set(CscpOrg::getWestoneOrgId, currentUasOrgRes.getOrgId());
                cscpOrgService.update(null, updateWrapper);
            }
        } else {
            UasOrg currentUasOrg = new UasOrg();
            currentUasOrg.setOrgId(syncWestoneOrgDTO.getWestoneOrgId());
            currentUasOrg.setCode(syncWestoneOrgDTO.getOrgCode());
            currentUasOrg.setName(syncWestoneOrgDTO.getOrgName());
//                    currentUasOrg.setType("2");
            currentUasOrg.setAddress(syncWestoneOrgDTO.getRegionCode());
            currentUasOrg.setParentId(westonOrgParentId);

            log.info("同步组织机构信息到到卫士通统一身份认证系统，更新组织机构，请求参数{}", JSON.toJSONString(currentUasOrg));
            currentUasOrgRes = uasApiInstance.updateOrg(getAccessToken(uasApiInstance), currentUasOrg);

            // 记录推送日志
            addPushOrgLog(applicationId, syncWestoneOrgDTO, currentUasOrgRes, WestoneUasApiUtil.OPT_UPDATE);
        }
        return currentUasOrgRes;
    }

    private List<SyncWestoneOrgDTO> getOrgTree(List<SyncWestoneOrgDTO> cscpOrgs, Long parentId) {
        List<SyncWestoneOrgDTO> orgs = cscpOrgs.stream()
                .filter(x -> x.getParentId() == parentId)
                .map(t -> {
                    t.setChildren(getChildOrgTree(t, cscpOrgs));
                    return t;
                }).collect(Collectors.toList());
        return orgs;
    }

    private List<SyncWestoneOrgDTO> getChildOrgTree(SyncWestoneOrgDTO rootNode, List<SyncWestoneOrgDTO> allNode) {
        List<SyncWestoneOrgDTO> childrenOrgs = allNode.stream()
                .filter(x -> Objects.equals(x.getParentId(), rootNode.getId()))
                .map(t -> {
                    t.setChildren(getChildOrgTree(t, allNode));
                    return t;
                }).collect(Collectors.toList());
        return childrenOrgs;
    }

    /**
     * 添加推送组织机构日志记录
     * @param appId
     * @param syncWestoneOrgDTO
     * @param resp
     * @param strOperaType
     */
    private void addPushOrgLog(Long appId, SyncWestoneOrgDTO syncWestoneOrgDTO, UasOrgOptRes resp, String strOperaType) {
        TSyncOrgHistroyRecord insertRecord = new TSyncOrgHistroyRecord();
        insertRecord.setAppId(appId);
        if (null != syncWestoneOrgDTO) {
            insertRecord.setOrgId(syncWestoneOrgDTO.getId());
            insertRecord.setParentId(syncWestoneOrgDTO.getParentId());
            insertRecord.setStrUnitName(syncWestoneOrgDTO.getOrgName());
            insertRecord.setStrUnitCode(syncWestoneOrgDTO.getOrgCode());
            insertRecord.setStrAreaCode(syncWestoneOrgDTO.getRegionCode());
        }
        insertRecord.setInSystemFlag(0);
        insertRecord.setStrOperaType(strOperaType);
        insertRecord.setRequestMode(OrgUserConstants.RequestMode.REQUEST_MODE_HTTP);
        if (WestoneResEnum.SUCCESS.getCode() == resp.getCode()) {
            insertRecord.setSyncSuccess("true");
            insertRecord.setSyncMessage(resp.getMessage());
            insertRecord.setSyncStatus(resp.getCode() + "");
        } else {
            insertRecord.setSyncSuccess("false");
            insertRecord.setSyncMessage(resp.getMessage());
            insertRecord.setSyncStatus(resp.getCode() + "");
        }
        // 手动设置create_time，确保分表路由正确
        if (insertRecord.getCreateTime() == null) {
            insertRecord.setCreateTime(LocalDateTime.now());
        }
        this.tSyncOrgHistroyRecordMapper.insert(insertRecord);
    }


    /**
     * 同步用户数据
     * @param syncOrgUserDTO 用户同步请求参数
     * @param isDefaultOrg 是否为默认组织机构
     * @return 是否同步成功
     */
    @EnableTraceWatch(level = TraceLevel.INFO, notes = "同步用户信息到卫士通统一身份认证系统")
    @Override
    public boolean syncUserToWestoneUasSystem(SyncOrgUserDTO syncOrgUserDTO, boolean isDefaultOrg) {
        TSyncAppSystemManage appSystemManage = syncOrgUserDTO.getCompatibleApp(tSyncAppSystemManageMapper);
        CscpUser cscpUser = cscpUserService.getById(syncOrgUserDTO.getUserId());
        if ("delete".equals(syncOrgUserDTO.getFlag())) {
            log.info("同步用户信息到到卫士通统一身份认证系统 - 用户禁用 旧手机号:" + cscpUser.getOfficePhone());
            cscpUser.setMobile(cscpUser.getOfficePhone());
        }
        if (cscpUser == null || appSystemManage == null) {
            log.error("同步用户信息到到卫士通统一身份认证系统syncUserToWestoneUasSystem，请求参数有误");
            return false;
        }
//        if (0 == cscpUser.getStatus()) {
//            if (StringUtils.isNotEmpty(cscpUser.getOfficePhone())) {
//                cscpUser.setMobile(cscpUser.getOfficePhone());
//            }
//        }
        if (SysConstant.DELAULT_MOBILE.equals(cscpUser.getMobile())) {
            // TODO 测试号码，跳过推送
            log.error("同步用户信息到到卫士通统一身份认证系统syncUserToWestoneUasSystem，测试号码{}跳过推送", SysConstant.DELAULT_MOBILE);
            return true;
        }
        UasUserOptRes errorResp = new UasUserOptRes();
        errorResp.setCode(WestoneResEnum.FAILURE.getCode());

        // TODO 多个手机号码
        Set<String> mobilePhones = getMobilePhones(cscpUser);
        if (mobilePhones.isEmpty()) {
            errorResp.setMessage(MessageFormat.format("同步用户信息到到卫士通统一身份认证系统，" +
                            "用户[appId={0},userId={1},loginName={2}]未绑定手机号码",
                    syncOrgUserDTO.getAppId(), cscpUser.getId(), cscpUser.getLoginName()));
            // TODO 放弃推送
            log.info(errorResp.getMessage());
            // 记录推送日志
            addPushUserLog(appSystemManage.getId(), null, errorResp, WestoneUasApiUtil.OPT_ADD);
            return true;
        }
        // 手机号码Set集合-->卫士通用户ID的List集合  {a=d, b=e, c=f}
        Map<String, String> westoneUserIds = new HashMap<>();
        String westoneUserIdStr = cscpUser.getWestoneUserId();
        if (null != westoneUserIdStr && !"".equals(westoneUserIdStr)) {
            String[] westoneUserIdLst = westoneUserIdStr.split(",");
            Iterator<String> mobileIterator = mobilePhones.iterator();
            for (String value : westoneUserIdLst) {
                if (mobileIterator.hasNext()) {
                    String key = mobileIterator.next();
                    westoneUserIds.put(key, value);
                } else {
                    // 如果Set中的元素用完了，就停止遍历
                    break;
                }
            }
        }
        String allWestoneUserIdStr = "";
        try {
            for (String mobilePhone : mobilePhones) {
                UasUser uasUser = new UasUser();
//                uasUser.setAccount(cscpUser.getLoginName() + "-" + mobilePhone);
                String strId = cscpUser.getStrId();
                if (null == strId || "".equals(strId)) {
//                    log.info("同步用户信息到到卫士通统一身份认证系统失败，唯一信任号为空，跳过推送。用户id={}，用户名={}", cscpUser.getId(), cscpUser.getLoginName());
//                    continue;
                    strId = cscpUser.getId() + "";
                }
                uasUser.setAccount(strId + "-" + mobilePhone);
                // TODO 卫士通处理多个手机号码存储 一个主号码、多个备用号码
                uasUser.setPhone(mobilePhone);
                uasUser.setName(cscpUser.getRealName());
                uasUser.setLoginName(cscpUser.getLoginName());
                // 性别：1-男性，0-女性
                if (null == cscpUser.getSex()) {
                    uasUser.setGender("1");
                } else if (cscpUser.getSex() == 0) {
                    uasUser.setGender("1");
                } else {
                    uasUser.setGender("0");
                }
                uasUser.setCustomUserId(cscpUser.getId() + "");

                // TODO mainOrganizationId、organizationIds不传值，使用卫士通默认组织机构ID
                if (!isDefaultOrg) {
                    // 查询用户所属机构
                    LambdaQueryWrapper<CscpUserOrg> userOrgQW = new LambdaQueryWrapper();
                    userOrgQW.eq(CscpUserOrg::getUserId, cscpUser.getId());
                    userOrgQW.eq(CscpUserOrg::getDefaultDepartment, 1);
                    userOrgQW.orderByAsc(CscpUserOrg::getOrderBy);
                    List<CscpUserOrg> cscpUserOrgs = cscpUserOrgService.selectListNoAdd(userOrgQW);
                    if (cscpUserOrgs != null && !cscpUserOrgs.isEmpty()) {
                        Long orgId = cscpUserOrgs.get(0).getOrgId();
                        CscpOrg cscpOrg = cscpOrgService.getById(orgId);
                        if (null == cscpOrg.getWestoneOrgId() || "".equals(cscpOrg.getWestoneOrgId())) {
//                        errorResp.setMessage(MessageFormat.format("同步用户信息到到卫士通统一身份认证系统，" +
//                                        "用户[appId={0},userId={1},loginName={2}]未绑定单位",
//                                syncOrgUserDTO.getAppId(), cscpUser.getId(), cscpUser.getLoginName()));
//                        // TODO 放弃推送
//                        log.info(errorResp.getMessage());
//                        // 记录推送日志
//                        addPushUserLog(appSystemManage.getId(), uasUser, errorResp, WestoneUasApiUtil.OPT_ADD);
//                        return false;

                            syncOrgUserDTO.setOrgId(orgId);
                            boolean isAddOrg = syncOrgToWestoneUasSystem(syncOrgUserDTO);
                            if (isAddOrg) {
                                cscpOrg = cscpOrgService.getById(orgId);
                                uasUser.setMainOrganizationId(cscpOrg.getWestoneOrgId());
                                uasUser.setOrganizationIds(new String[]{cscpOrg.getWestoneOrgId()});
                            } else {
                                errorResp.setMessage(MessageFormat.format("同步用户信息到到卫士通统一身份认证系统，" +
                                                "用户[appId={0},userId={1},loginName={2}]未绑定单位",
                                        syncOrgUserDTO.getAppId(), cscpUser.getId(), cscpUser.getLoginName()));
                                // TODO 放弃推送
                                log.info(errorResp.getMessage());
                                // 记录推送日志
                                addPushUserLog(appSystemManage.getId(), uasUser, errorResp, WestoneUasApiUtil.OPT_ADD);
                                return false;
                            }
                        } else {
                            uasUser.setMainOrganizationId(cscpOrg.getWestoneOrgId());
                            uasUser.setOrganizationIds(new String[]{cscpOrg.getWestoneOrgId()});
                        }
                    }
                }

                UasUserOptRes resp = null;
                WestoneUasApiUtil uasApiInstance = getUasApiInstance(appSystemManage);
//                if (null == cscpUser.getWestoneUserId() || "".equals(cscpUser.getWestoneUserId())) {
                if (!westoneUserIds.containsKey(mobilePhone)) {
                    log.info("同步用户信息到到卫士通统一身份认证系统，新增用户，请求参数{}", JSON.toJSONString(uasUser));
                    resp = uasApiInstance.createUser(getAccessToken(uasApiInstance), uasUser);

                    // 记录推日志
                    addPushUserLog(appSystemManage.getId(), uasUser, resp, WestoneUasApiUtil.OPT_ADD);
                } else {
//                    uasUser.setUserId(cscpUser.getWestoneUserId());
                    uasUser.setUserId(westoneUserIds.get(mobilePhone));

                    // TODO 用户禁用状态
                    if (null != cscpUser.getDeleted() && 1 == cscpUser.getDeleted()) {
                        // 禁用用户 不做删除 仅禁用该用户
//                        uasUser = new UasUser();
//                        uasUser.setUserId(cscpUser.getWestoneUserId());
//                        log.info("同步用户信息到到卫士通统一身份认证系统，禁用用户，请求参数{}", JSON.toJSONString(uasUser));
//                        resp = uasApiInstance.disableUser(getAccessToken(uasApiInstance), uasUser);
//
//                        // 记录推送日志
//                        addPushUserLog(appSystemManage.getId(), uasUser, resp, WestoneUasApiUtil.OPT_DELETE);
                    } else {
//                        if (null != cscpUser.getStatus() && "0".equals(cscpUser.getStatus().toString())) {
//                            // 禁用用户 不做删除 仅禁用该用户
//                            uasUser.setStatus(2);
//                        } else {
//                            // 启用用户
//                            uasUser.setStatus(1);
//                        }
                        uasUser.setStatus(1);
                        log.info("同步用户信息到到卫士通统一身份认证系统，更新用户，请求参数{}", JSON.toJSONString(uasUser));
                        resp = uasApiInstance.updateUser(getAccessToken(uasApiInstance), uasUser);

                        // 记录推送日志
                        addPushUserLog(appSystemManage.getId(), uasUser, resp, WestoneUasApiUtil.OPT_UPDATE);
                    }
                }

                if (null != resp.getUserId()) {
                    allWestoneUserIdStr += resp.getUserId() + ",";
                }
            }

            if (null != allWestoneUserIdStr && !"".equals(allWestoneUserIdStr)) {
                if (allWestoneUserIdStr.endsWith(",")) {
                    allWestoneUserIdStr = allWestoneUserIdStr.substring(0, allWestoneUserIdStr.length() - 1);
                }

                // TODO 更新
                LambdaUpdateWrapper<CscpUser> updateWrapper = Wrappers.lambdaUpdate();
                updateWrapper.eq(CscpUser::getId, cscpUser.getId())
                        .set(CscpUser::getWestoneUserId, allWestoneUserIdStr);
                cscpUserService.update(null, updateWrapper);
            }
        } catch (Exception e) {
            log.error("同步用户信息到卫士通统一身份认证系统[appId={},userId={},loginName={}]请求接口失败：{}",
                    syncOrgUserDTO.getAppId(), cscpUser.getId(), cscpUser.getLoginName(), e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * 一键同步所有同步用户数据
     *
     * @param syncOrgUserDTO
     * @return
     */
    @EnableTraceWatch(level = TraceLevel.INFO, notes = "一键批量同步所有用户信息到卫士通统一身份认证系统")
    @Override
    public boolean syncAllUserToWestoneUasSystem(SyncOrgUserDTO syncOrgUserDTO, boolean isDefaultOrg, boolean isPushed) {
        // 分页查询用户数据
        LambdaQueryWrapper<CscpUser> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        if (null != syncOrgUserDTO.getUserId() && !"".equals(syncOrgUserDTO.getUserId())) {
            lambdaQueryWrapper.eq(CscpUser::getId, syncOrgUserDTO.getUserId());
        }
        if (!isPushed) {
            lambdaQueryWrapper.eq(CscpUser::getWestoneUserId, null).or().eq(CscpUser::getWestoneUserId, "");
        }
        // 其他查询条件，根据实际情况添加
        BasePageForm basePageForm = new BasePageForm();
        int pageSize = 50;
        try {
            int totalCount = cscpUserService.count(lambdaQueryWrapper);
            log.info("一键同步所有用户数据到卫士通统一身份认证系统，分批处理，总条数：{}，分批处理，每次处理条数：{}", totalCount, pageSize);
            int totalPages = (totalCount + pageSize - 1) / pageSize;
            for (int page = 1; page <= totalPages; page++) {
                basePageForm.setCurrentPage(page);
                basePageForm.setPageSize(pageSize);
                IPage<CscpUser> userIPage = cscpUserService.selectPageNoAdd(
                        PageHelperUtil.getMPlusPageByBasePage(basePageForm), lambdaQueryWrapper);
                List<CscpUser> cscpUsers = userIPage.getRecords();
                log.info("[创建或更新用户]一键同步所有用户数据到卫士通统一身份认证系统，分批处理，当前批次：{}，处理条数：{}", page, cscpUsers.size());
                for (CscpUser cscpUser : cscpUsers) {
                    SyncOrgUserDTO pageSyncOrgUserDTO = new SyncOrgUserDTO();
                    pageSyncOrgUserDTO.setAppId(syncOrgUserDTO.getAppId());
                    pageSyncOrgUserDTO.setUserId(cscpUser.getId());
                    try {
                        syncUserToWestoneUasSystem(pageSyncOrgUserDTO, isDefaultOrg);
                    } catch (Exception ex) {
                        // TODO ignore
                    }
                }
            }
        } catch (Exception e) {
            log.error("同步用户信息到卫士通统一身份认证系统[appId={}]请求接口失败：{}",
                    syncOrgUserDTO.getAppId(), e.getMessage());
            return false;
        }
        return true;
    }

    /**
     * 一键同步所有同步用户数据
     *
     * @param syncOrgUserDTO
     * @return
     */
    @EnableTraceWatch(level = TraceLevel.INFO, notes = "一键批量同步所有用户信息到卫士通统一身份认证系统")
    @Override
    public boolean syncAllUserToWestoneUasSystem(SyncOrgUserDTO syncOrgUserDTO, boolean isDefaultOrg) {
        return syncAllUserToWestoneUasSystem(syncOrgUserDTO, isDefaultOrg, true);
    }

    /**
     * 一键同步所有同步用户数据
     *
     * @param syncOrgUserDTO
     * @return
     */
    @EnableTraceWatch(level = TraceLevel.INFO, notes = "一键批量同步所有用户信息到卫士通统一身份认证系统")
    @Override
    public boolean syncAllUnPushedUserToWestoneUasSystem(SyncOrgUserDTO syncOrgUserDTO, boolean isDefaultOrg) {
        return syncAllUserToWestoneUasSystem(syncOrgUserDTO, isDefaultOrg, false);
    }

    /**
     * 获取用户的手机号码集合
     * @param cscpUser
     * @return
     */
    private Set<String> getMobilePhones(CscpUser cscpUser) {
        Set<String> mobilePhones = new HashSet<>();
        if (null != cscpUser.getMobile() && !"".equals(cscpUser.getMobile())) {
            mobilePhones.add(cscpUser.getMobile().trim());
        }
        if (null != cscpUser.getBackupMobile() && !"".equals(cscpUser.getBackupMobile())) {
            String[] backupMobiles = cscpUser.getBackupMobile().split(",");
            for (String backupMobile : backupMobiles) {
                if (null != backupMobile && !"".equals(backupMobile)) {
                    if (!SysConstant.DELAULT_MOBILE.equals(cscpUser.getMobile())) {
                        mobilePhones.add(backupMobile.trim());
                    }
                }
            }
        }
        return mobilePhones;
    }

    /**
     * 添加推送用户日志记录
     * @param appId
     * @param uasUser
     * @param resp
     * @param strOperaType
     */
    private void addPushUserLog(Long appId, UasUser uasUser, UasUserOptRes resp, String strOperaType) {
        TSyncUserHistroyRecord insertRecord = new TSyncUserHistroyRecord();
        insertRecord.setAppId(appId);
        if (null != uasUser) {
            insertRecord.setUserId(Long.parseLong(uasUser.getCustomUserId()));
            insertRecord.setStrMobile(uasUser.getPhone());
            insertRecord.setStrCname(uasUser.getName());
            insertRecord.setStrSex(uasUser.getGender());
            insertRecord.setStrId(uasUser.getUserId());
            insertRecord.setLoginName(uasUser.getLoginName());
        }
        insertRecord.setStrOperaType(strOperaType);
        insertRecord.setRequestMode(OrgUserConstants.RequestMode.REQUEST_MODE_HTTP);
        insertRecord.setInSystemFlag(0);
        if (WestoneResEnum.SUCCESS.getCode() == resp.getCode()) {
            insertRecord.setSyncSuccess("true");
            insertRecord.setSyncMessage(resp.getMessage());
            insertRecord.setSyncStatus(resp.getCode() + "");
        } else {
            insertRecord.setSyncSuccess("false");
            insertRecord.setSyncMessage(resp.getMessage());
            insertRecord.setSyncStatus(resp.getCode() + "");
        }
        // 手动设置create_time，确保分表路由正确
        if (insertRecord.getCreateTime() == null) {
            insertRecord.setCreateTime(LocalDateTime.now());
        }
        this.tSyncUserHistroyRecordMapper.insert(insertRecord);
    }

    /**
     * 添加推送用户日志记录
     * @param syncUserHistroyRecordDTO
     */
    public void addPushUserLog(SyncUserHistroyRecordDTO syncUserHistroyRecordDTO) {
        TSyncUserHistroyRecord insertRecord = BeanConvertUtils.copyProperties(syncUserHistroyRecordDTO, TSyncUserHistroyRecord.class);
        TSyncAppSystemManage appSystemManage = tSyncAppSystemManageMapper.selectOneNoAdd(
                new LambdaQueryWrapper<TSyncAppSystemManage>().eq(TSyncAppSystemManage::getAppCode, WESTONE_UAS_SYSTEM));
        insertRecord.setAppId(appSystemManage.getId());
        insertRecord.setRequestMode(OrgUserConstants.RequestMode.REQUEST_MODE_HTTP);
        // 手动设置create_time，确保分表路由正确
        if (insertRecord.getCreateTime() == null) {
            insertRecord.setCreateTime(LocalDateTime.now());
        }
        this.tSyncUserHistroyRecordMapper.insert(insertRecord);
    }

}
