package com.ctsi.hndxoa.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.ListCopyUtil;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.entity.dto.TSyncAppSystemManageCompany.TSyncAppSystemManageCompanyDTO;
import com.ctsi.hndxoa.mapper.TSyncAppSystemManageMapper;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.hndxoa.entity.TSyncAppModeratorManage;
import com.ctsi.hndxoa.entity.dto.TSyncAppModeratorManageDTO;
import com.ctsi.hndxoa.mapper.TSyncAppModeratorManageMapper;
import com.ctsi.hndxoa.service.ITSyncAppModeratorManageService;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.springframework.transaction.annotation.Transactional;

/**
 * <p>
 * 版主应用管理表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Slf4j
@Service
public class TSyncAppModeratorManageServiceImpl extends SysBaseServiceImpl<TSyncAppModeratorManageMapper, TSyncAppModeratorManage> implements ITSyncAppModeratorManageService {

    @Autowired
    private TSyncAppModeratorManageMapper tSyncAppModeratorManageMapper;
    //@Autowired
    //private TSyncAppSystemManageMapper tSyncAppSystemManageMapper;
    /**
     * 翻页
     *
     * @param entityDTO
     * @param basePageForm
     * @return
     */
    @Override
    public PageResult<TSyncAppModeratorManageDTO> queryListPage(TSyncAppModeratorManageDTO entityDTO, BasePageForm basePageForm) {
        //设置条件
        LambdaQueryWrapper<TSyncAppModeratorManage> queryWrapper = new LambdaQueryWrapper();

        IPage<TSyncAppModeratorManage> pageData = tSyncAppModeratorManageMapper.selectPage(
             PageHelperUtil.getMPlusPageByBasePage(basePageForm), queryWrapper);
        //返回
        IPage<TSyncAppModeratorManageDTO> data  = pageData.convert(entity -> BeanConvertUtils.copyProperties(entity,TSyncAppModeratorManageDTO.class));

        return new PageResult<TSyncAppModeratorManageDTO>(data.getRecords(),
            data.getTotal(), data.getCurrent());
    }

    /**
     * 列表查询
     *
     * @param entityDTO
     * @return
     */
    @Override
    public List<TSyncAppModeratorManageDTO> queryList(TSyncAppModeratorManageDTO entityDTO) {
        LambdaQueryWrapper<TSyncAppModeratorManage> queryWrapper = new LambdaQueryWrapper();
            List<TSyncAppModeratorManage> listData = tSyncAppModeratorManageMapper.selectList(queryWrapper);
            List<TSyncAppModeratorManageDTO> TSyncAppModeratorManageDTOList = ListCopyUtil.copy(listData, TSyncAppModeratorManageDTO.class);
        return TSyncAppModeratorManageDTOList;
    }

    /**
     * 查询当前用户的版主应用id列表
     *
     * @return
     */
    @Override
    public List<Long> getCurrentModeratorManageAppList(Long userId) {
        LambdaQueryWrapper<TSyncAppModeratorManage> queryWrapper = new LambdaQueryWrapper();
        queryWrapper.select(TSyncAppModeratorManage::getAppId);
        queryWrapper.eq(userId !=null, TSyncAppModeratorManage::getModeratorId, userId);
        List<Long> appIdList = tSyncAppModeratorManageMapper.selectListNoAdd(queryWrapper).stream().map(TSyncAppModeratorManage::getAppId).distinct().collect(Collectors.toList());
        return appIdList;
    }

    @Override
    public List<Long> getCurrentModeratorManageAppListByCompany(Long userId) {
        Long currentCompanyId = SecurityUtils.getCurrentCompanyId();
        if (currentCompanyId == null) {
            return getCurrentModeratorManageAppList(userId);
        }

        List<Long> appIdList = tSyncAppModeratorManageMapper.selectAppIdsByUserIdAndCompanyId(userId, currentCompanyId);
        return appIdList.stream().distinct().collect(Collectors.toList());
    }

    /**
     * 单个查询
     *
     * @param id the id of the entity
     * @return
     */
    @Override
    public TSyncAppModeratorManageDTO findOne(Long id) {
        TSyncAppModeratorManage  tSyncAppModeratorManage =  tSyncAppModeratorManageMapper.selectById(id);
        return  BeanConvertUtils.copyProperties(tSyncAppModeratorManage,TSyncAppModeratorManageDTO.class);
    }


    /**
     * admin授权版主独立管理应用
     *
     * @param record the entity to create
     * @return
     */
    //@Override
    //@Transactional(rollbackFor = Exception.class)
    //public TSyncAppModeratorManageDTO assignModerator(TSyncAppSystemManageCompanyDTO record) {
    //    // 1. 参数校验
    //    if (CollectionUtil.isEmpty(record.getAppIds())) {
    //        throw new BusinessException("appIds不能为空");
    //    }
    //
    //    List<Long> userIdList = record.getUserIds();
    //    Set<Long> newModeratorIds = CollectionUtil.isNotEmpty(userIdList) ? new HashSet<>(userIdList) : Collections.emptySet();
    //
    //    // 查询所有涉及的应用的版主关系
    //    List<TSyncAppModeratorManage> existingList = tSyncAppModeratorManageMapper.selectList(
    //            Wrappers.<TSyncAppModeratorManage>lambdaQuery()
    //                    .in(TSyncAppModeratorManage::getAppId, record.getAppIds())
    //    );
    //
    //    // 构建map: appId -> (moderatorId -> record)
    //    Map<Long, Map<Long, TSyncAppModeratorManage>> existingMap = existingList.stream()
    //            .collect(Collectors.groupingBy(
    //                    TSyncAppModeratorManage::getAppId,
    //                    Collectors.toMap(TSyncAppModeratorManage::getModeratorId, Function.identity())
    //            ));
    //
    //    // 需要删除和插入的主键集合
    //    List<Long> toDeleteIds = new ArrayList<>();
    //    List<TSyncAppModeratorManage> toInsertList = new ArrayList<>();
    //
    //    for (Long appId : record.getAppIds()) {
    //        Map<Long, TSyncAppModeratorManage> appModeratorMap = existingMap.getOrDefault(appId, Collections.emptyMap());
    //        Set<Long> existingModeratorIds = appModeratorMap.keySet();
    //
    //        // 找出要删除的
    //        existingModeratorIds.stream()
    //                .filter(id -> !newModeratorIds.contains(id))
    //                .map(appModeratorMap::get)
    //                .map(TSyncAppModeratorManage::getId)
    //                .forEach(toDeleteIds::add);
    //
    //        // 找出要新增的
    //        newModeratorIds.stream()
    //                .filter(id -> !existingModeratorIds.contains(id))
    //                .forEach(id -> {
    //                    TSyncAppModeratorManage manage = new TSyncAppModeratorManage();
    //                    manage.setAppId(appId);
    //                    manage.setModeratorId(id);
    //                    toInsertList.add(manage);
    //                });
    //    }
    //
    //    // 批量删除
    //    if (CollectionUtil.isNotEmpty(toDeleteIds)) {
    //        tSyncAppModeratorManageMapper.deleteBatchIds(toDeleteIds);
    //    }
    //
    //    // 批量插入
    //    if (CollectionUtil.isNotEmpty(toInsertList)) {
    //        saveBatch(toInsertList);
    //    }
    //
    //    // 如果userIdList为空，特殊处理：全部删除
    //    if (CollectionUtil.isEmpty(userIdList)) {
    //        tSyncAppModeratorManageMapper.delete(Wrappers.<TSyncAppModeratorManage>lambdaQuery().in(TSyncAppModeratorManage::getAppId, record.getAppIds())
    //        );
    //    }
    //
    //    // 批量更新应用表中的版主标志位
    //    Integer moderatorFlag = CollectionUtil.isNotEmpty(userIdList) ? 1 : 0;
    //    for (Long appId : record.getAppIds()) {
    //        TSyncAppSystemManage updateApp = new TSyncAppSystemManage();
    //        updateApp.setId(appId);
    //        updateApp.setModeratorFlag(moderatorFlag);
    //        tSyncAppSystemManageMapper.updateById(updateApp);
    //    }
    //
    //    return null;
    //}

    /**
     * 修改
     *
     * @param entity the entity to update
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int update(TSyncAppModeratorManageDTO entity) {
        TSyncAppModeratorManage tSyncAppModeratorManage = BeanConvertUtils.copyProperties(entity,TSyncAppModeratorManage.class);
        return tSyncAppModeratorManageMapper.updateById(tSyncAppModeratorManage);
    }

    /**
     * 删除
     *
     * @param id the id of the entity
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int delete(Long id) {
        return tSyncAppModeratorManageMapper.deleteById(id);
    }


    /**
     * 验证是否存在
     *
     * @param TSyncAppModeratorManageId
     * @return
     */
    @Override
    public boolean existByTSyncAppModeratorManageId(Long TSyncAppModeratorManageId) {
        if (TSyncAppModeratorManageId != null) {
            LambdaQueryWrapper<TSyncAppModeratorManage> queryWrapper = new LambdaQueryWrapper();
            queryWrapper.eq(TSyncAppModeratorManage::getId, TSyncAppModeratorManageId);
            List<TSyncAppModeratorManage> result = tSyncAppModeratorManageMapper.selectList(queryWrapper);
            return result.size() > 0;
        }
        return true;
    }

    /**
    * 批量新增
    *
    */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean insertBatch(List<TSyncAppModeratorManageDTO> dataList) {
        List<TSyncAppModeratorManage> result = ListCopyUtil.copy(dataList, TSyncAppModeratorManage.class);
        return saveBatch(result);
    }


}
