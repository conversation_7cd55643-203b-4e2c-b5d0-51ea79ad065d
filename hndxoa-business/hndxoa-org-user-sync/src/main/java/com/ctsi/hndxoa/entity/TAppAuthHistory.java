package com.ctsi.hndxoa.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ctsi.hndx.common.BaseEntity;
import com.ctsi.hndxoa.mapper.TSyncAppSystemManageMapper;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 应用授权市县区单位
 * @ TableName t_app_auth_history
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("t_app_auth_history")
@ApiModel(value = "TAppAuthHistory对象", description = "APP授权记录")
public class TAppAuthHistory extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 应用id
     */
    @ApiModelProperty("应用id")
    private Long appId;

    /**
     * 应用名称 展示 查询
     */
    @ApiModelProperty("应用名称")
    private String appName;

    /**
     * 应用编码 展示 查询
     */
    @ApiModelProperty("应用编码")
    private String appCode;

    /**
     * 机构id
     */
    @ApiModelProperty("机构id")
    private Long orgId;

    /**
     * 机构名称 展示 查询
     */
    @ApiModelProperty("机构名称")
    private String orgName;

    /**
     * 用户id
     */
    @ApiModelProperty("用户id")
    private Long userId;

    /**
     * 用户名
     */
    @ApiModelProperty("用户名")
    private String userName;

    // 展示
    @ApiModelProperty("记录原因")
    private String documentReason;

    // 授权类型 展示 查询
    private String optType;

    // 授权状态 0-已失效，1-生效中 展示 查询
    private Integer status;

    /**
     * 创建时间 查询createTimeStart、createTimeEnd  展示createTime
     */
    @ApiModelProperty("创建时间从")
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStart;
    /**
     * 创建时间 查询
     */
    @ApiModelProperty("创建时间至")
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;
    // createName 展示， updateName 展示

    public enum OptTypeEnum {
        ADD("新增授权"),
        DELETE("取消授权"),
        ROllBACK("回滚");
        private String value;
        OptTypeEnum(String value) {
            this.value = value;
        }
        public String getValue() {
            return value;
        }
    }


    // 缓存 appId -> TSyncAppSystemManage
    private static final Cache<Long, TSyncAppSystemManage> appByIdCache = CacheBuilder.newBuilder()
            .expireAfterWrite(24, TimeUnit.HOURS)
            .maximumSize(50)
            .build();

    // 缓存 appCode -> TSyncAppSystemManage
    private static final Cache<String, TSyncAppSystemManage> appByCodeCache = CacheBuilder.newBuilder()
            .expireAfterWrite(24, TimeUnit.HOURS)
            .maximumSize(50)
            .build();

    // 缓存 userId -> Username
    private static final Cache<Long, String> userByIdCache = CacheBuilder.newBuilder()
            .expireAfterWrite(24, TimeUnit.HOURS)
            .maximumSize(100000)
            .build();

    // 缓存 orgId -> orgName
    private static final Cache<Long, String> orgByIdCache = CacheBuilder.newBuilder()
            .expireAfterWrite(24, TimeUnit.HOURS)
            .maximumSize(100000)
            .build();

    public void setAppInfo(Long appId, String appCode, TSyncAppSystemManageMapper tSyncAppSystemManageMapper) {
        if (appId != null) {
            TSyncAppSystemManage appSystemManage = appByIdCache.getIfPresent(appId);
            if (appSystemManage == null) {
                appSystemManage = tSyncAppSystemManageMapper.selectById(appId);
                if (appSystemManage != null) {
                    appByIdCache.put(appId, appSystemManage);
                }
            }
            if (appSystemManage != null) {
                this.appId = appId;
                this.appName = appSystemManage.getAppName();
                this.appCode = appSystemManage.getAppCode();
            }
        } else if (appCode != null) {
            TSyncAppSystemManage appSystemManage = appByCodeCache.getIfPresent(appCode);
            if (appSystemManage == null) {
                LambdaQueryWrapper<TSyncAppSystemManage> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                lambdaQueryWrapper.eq(TSyncAppSystemManage::getAppCode, appCode);
                appSystemManage = tSyncAppSystemManageMapper.selectOneNoAdd(lambdaQueryWrapper);
                if (appSystemManage != null) {
                    appByCodeCache.put(appCode, appSystemManage);
                }
            }
            if (appSystemManage != null) {
                this.appId = appSystemManage.getId();
                this.appName = appSystemManage.getAppName();
                this.appCode = appSystemManage.getAppCode();
            }
        }
    }

    public void setUserInfo(Long userId, CscpUserRepository cscpUserRepository) {
        if (userId != null) {
            String username = userByIdCache.getIfPresent(userId);
            if (username == null) {
                CscpUserDTO cscpUserDTO = cscpUserRepository.selectUserById(userId);
                if (cscpUserDTO != null) {
                    username = cscpUserDTO.getLoginName();
                    userByIdCache.put(userId, username);
                }
            }
            if (username != null) {
                this.userId = userId;
                this.userName = username;
            }
        }
    }

    public void setOrgInfo(Long orgId, CscpOrgRepository cscpOrgRepository) {
        if (orgId != null) {
            String orgName = orgByIdCache.getIfPresent(orgId);
            if (orgName == null) {
                CscpOrg cscpOrg = cscpOrgRepository.selectById(orgId);
                if (cscpOrg != null) {
                    orgName = cscpOrg.getOrgName();
                    orgByIdCache.put(orgId, orgName);
                }
            }
            if (orgName != null) {
                this.orgId = orgId;
                this.orgName = orgName;
            }
        }
    }

    public void setCscpUserDetail(CscpUserDetail cscpUserDetail) {
        if (cscpUserDetail == null) {
            return;
        }
        setDepartmentId(cscpUserDetail.getDepartmentId());
        setCompanyId(cscpUserDetail.getCompanyId());
        setTenantId(cscpUserDetail.getTenantId());
        setUpdateBy(cscpUserDetail.getId());
        setUpdateName(cscpUserDetail.getUsername());
        if (getId() == null) {
            setCreateBy(cscpUserDetail.getId());
            setCreateName(cscpUserDetail.getUsername());
        }
    }
}
