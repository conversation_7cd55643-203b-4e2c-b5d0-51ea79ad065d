package com.ctsi.hndxoa.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndxoa.entity.dto.TMqDlx;
import com.ctsi.hndxoa.entity.dto.TSyncOrgHistroyRecordDTO;
import com.ctsi.hndxoa.service.ITMqDlxService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/mq/dlx")
@Api(value = "同步机构历史记录表", tags = "同步机构历史记录表接口")
public class TMqDlxController extends BaseController {

    @Autowired
    private ITMqDlxService itMqDlxService;


     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @ApiOperation(value = "删除存在数据（物理删除）", notes = "传入参数")
    public ResultVO delete(@PathVariable Long id) {
        int count = itMqDlxService.delete(id);
        return ResultVO.success(count);
    }

    /**
     *  删除存在数据.
     */
    @PostMapping("/delete/batch")
    @ApiOperation(value = "批量删除存在数据（物理删除）", notes = "传入参数")
    public ResultVO delete(@RequestBody List<Long> ids) {
        itMqDlxService.batchDelete(ids);
        return ResultVO.success();
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/page")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    public ResultVO<PageResult<TMqDlx>> pageTMqDlx(TMqDlx tMqDlx, BasePageForm basePageForm) {
        return ResultVO.success(itMqDlxService.pageTMqDlx(tMqDlx, basePageForm));
    }



}
