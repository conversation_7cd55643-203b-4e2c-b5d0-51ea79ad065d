package com.ctsi.hndxoa.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.ctsi.hndx.config.DbConst;
import com.ctsi.hndx.westone.WestoneEncryptService;
import com.ctsi.hndxoa.entity.TSyncUserHistroyRecord;
import com.ctsi.hndxoa.mapper.TSyncUserHistroyRecordMapper;
import com.ctsi.hndxoa.mq.dto.MqResponseDTO;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.CscpUserOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.service.CscpUserOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class UserSyncOAConsumer {

    @Autowired
    CscpUserService cscpUserService;

    @Autowired
    CscpUserOrgService cscpUserOrgService;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "userSyncQueue", durable = "true"),
            exchange = @Exchange(value = "userSyncExchange", type = "topic"),
            key = "userSyncQueueOA"
    ))
    public void onMessage(String message, Message amqpMessage) {
        log.info("userChangePasswordQueue收到消息：" + message);
        CscpUserDTO cscpUserOA = JSONObject.parseObject(message, CscpUserDTO.class);
        if (StringUtils.isNotBlank(cscpUserOA.getStrId())) {
            CscpUser cscpUser = cscpUserService.selectUserByStrId(cscpUserOA.getStrId());
            if (cscpUser == null || (cscpUser.getUpdateTime() == null && cscpUserOA.getUpdateTime() == null)) {
                return;
            }
            if ((cscpUserOA.getUpdateTime() != null && cscpUser.getUpdateTime() == null)
                    || (cscpUserOA.getUpdateTime() != null && cscpUser.getUpdateTime() != null && cscpUserOA.getUpdateTime().isAfter(cscpUser.getUpdateTime()))
            ) {
                if (cscpUserOA.getSex() != null) {
                    cscpUser.setSex(cscpUserOA.getSex());
                }
                if (cscpUserOA.getOrgList() != null && !cscpUserOA.getOrgList().isEmpty()) {
                    for (CscpUserOrg cscpUserOrgOA : cscpUserOA.getOrgList()) {
                        CscpUserOrg userOrg = cscpUserOrgService.getById(cscpUserOrgOA.getId());
                        if (userOrg != null) {
                            if (cscpUserOrgOA.getRank() != null) {
                                userOrg.setRank(cscpUserOrgOA.getRank());
                            }
                            if (cscpUserOrgOA.getPost() != null) {
                                userOrg.setPost(cscpUserOrgOA.getPost());
                            }
                            cscpUserOrgService.updateById(userOrg);
                        }
                    }
                }

                if (cscpUserOA.getEntryTime() != null) {
                    cscpUser.setEntryTime(cscpUserOA.getEntryTime());
                }
                if (cscpUserOA.getOfferTime() != null) {
                    cscpUser.setOfferTime(cscpUserOA.getOfferTime());
                }
                if (cscpUserOA.getRank() != null) {
                    cscpUser.setRank(cscpUserOA.getRank());
                }

            }

            cscpUserService.updateById(cscpUser);
            cscpUserService.clearUserCache(cscpUser.getLoginName());
            if (cscpUserOA.getMobile() != null) {
                cscpUserService.clearUserCacheUsernameOrMobile(cscpUserOA.getMobile());
            }
        }

    }

}
