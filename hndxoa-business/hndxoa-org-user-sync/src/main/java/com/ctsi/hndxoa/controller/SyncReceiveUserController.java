package com.ctsi.hndxoa.controller;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndxoa.entity.dto.SyncReceiveOrgDTO;
import com.ctsi.hndxoa.entity.dto.SyncReceiveUserDTO;
import com.ctsi.hndxoa.service.ISyncReceiveUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/sync/receive/user")
@Api(value = "同步应用系统管理表", tags = "同步应用系统管理表接口")
public class SyncReceiveUserController {

    @Autowired
    private ISyncReceiveUserService iSyncReceiveUserService;

    @ApiOperation(value = "同步其他系统-用户导入")
    @PostMapping("/import")
    public ResultVO<?> userImport(MultipartFile file) throws IOException {
        String source = "isImport";
        /*EasyExcel.read(file.getInputStream(), SyncReceiveUserDTO.class, new PageReadListener<SyncReceiveUserDTO>(dataList -> {
            iSyncReceiveUserService.saveBatchUser(dataList,source);
        })).sheet().doRead();*/
        List<SyncReceiveUserDTO> dataList =  EasyExcel.read(file.getInputStream())
                .head(SyncReceiveUserDTO.class).sheet().doReadSync();
        iSyncReceiveUserService.saveBatchUser(dataList,source);
        return ResultVO.success(true);
    }

    @ApiOperation(value = "其他系统-用户新增接口")
    @PostMapping("/saveUser")
    public ResultVO<?> saveUser(@RequestBody List<SyncReceiveUserDTO> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new BusinessException(ResultCode.PARAM_IS_INVALID);
        }
        String source = "isInterface";
        String result =iSyncReceiveUserService.saveBatchUser(list, source);
        return ResultVO.success(result);
    }
}
