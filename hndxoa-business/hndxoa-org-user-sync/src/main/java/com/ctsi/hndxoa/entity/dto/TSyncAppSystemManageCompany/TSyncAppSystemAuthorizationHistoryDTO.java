package com.ctsi.hndxoa.entity.dto.TSyncAppSystemManageCompany;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDateTime;

@Data
@ApiModel(value = "TSyncAppSystemAuthorizationHistoryDTO", description = "授权历史记录DTO")
public class TSyncAppSystemAuthorizationHistoryDTO {
    
    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    private Long id;
    
    /**
     * 应用ID
     */
    @ApiModelProperty("应用ID")
    private Long appId;
    
    /**
     * 应用名称
     */
    @ApiModelProperty("应用名称")
    private String appName;
    
    /**
     * 应用编码
     */
    @ApiModelProperty("应用编码")
    private String appCode;
    
    /**
     * 机构ID
     */
    @ApiModelProperty("机构ID")
    private Long orgId;
    
    /**
     * 机构名称
     */
    @ApiModelProperty("机构名称")
    private String orgName;
    
    /**
     * 授权状态 (ACTIVE-有效, REVOKED-已撤销)
     */
    @ApiModelProperty("授权状态")
    private String status;

    private String createName;
    
    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    private LocalDateTime updateTime;
}