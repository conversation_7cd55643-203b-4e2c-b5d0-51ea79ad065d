package com.ctsi.hndxoa.mq.producer;

import com.ctsi.hndxoa.mq.consumer.HistoryCountConsumer;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 *
 */
@Service
public class HistoryCountProducer {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void sendHistoryCountMessage(String message) {
        rabbitTemplate.convertAndSend(HistoryCountConsumer.EXCHANGE, HistoryCountConsumer.ROUTING_KEY, message);
    }
}
