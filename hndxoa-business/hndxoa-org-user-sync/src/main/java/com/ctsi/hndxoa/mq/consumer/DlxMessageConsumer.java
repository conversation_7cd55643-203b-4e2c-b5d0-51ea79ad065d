package com.ctsi.hndxoa.mq.consumer;

/**
 * <AUTHOR> 15433
 * @date : 2025/04/09/11:00
 * description: 手搓死信队列批量定时消费
 */
import com.ctsi.hndxoa.entity.dto.TMqDlx;
import com.ctsi.hndxoa.service.ITMqDlxService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class DlxMessageConsumer {

    @Autowired
    private DlxMessageQueue dlxMessageQueue;
    @Autowired
    private ITMqDlxService itMqDlxService;
    private static final Logger log = LoggerFactory.getLogger(DlxMessageConsumer.class);

    @Scheduled(cron = "0 0 * * * ? ")
    public void processDlxMessages() {
        log.info("processDlxMessages");
        List<TMqDlx> messages = new ArrayList<>();
        int maxBatchSize = 1000;
        int count = 0;

        TMqDlx message;
        // 限制每次处理的消息数量，避免循环不断调用 dlxMessageQueue.poll()时间过长，与saveBatch里的DEFAULT_BATCH_SIZE保持一致
        while ((message = dlxMessageQueue.poll()) != null && count < maxBatchSize) {
            messages.add(message);
            count++;
        }

        if (!messages.isEmpty()) {
            try {
                itMqDlxService.saveBatch(messages);
                log.info("批量保存了 {} 条死信消息", messages.size());
            } catch (Exception e) {
                log.error("批量保存死信消息失败: {}", e.toString());
            }
        }
    }
}