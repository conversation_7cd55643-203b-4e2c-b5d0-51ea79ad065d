package com.ctsi.hndxoa.entity.dto;

import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.ssdc.security.CscpUserDetail;
import lombok.Data;

import java.util.UUID;

/**
 * <AUTHOR> 15433
 * @date : 2025/06/17/15:18
 * description:
 */
@Data
public class HttpPushOrgDTO {

    private CscpUserDetail cscpUserDetail;

    // 单个应用用它
    private TSyncAppSystemManage appSystemManage;

    // ("新增的推送应用")
    private String addAppCodes;
    // ("更新的推送应用")
    private String updateAppCodes;
    // ("被取消授权的推送应用")
    private String removeAppCodes;

    // 新建、编辑用户时前端传过来的pushAppCode
    private String pushAppCode;

    private Long orgId;

    private String flag;

    private Boolean isAutoPushFlag = false;

    private String uuid = UUID.randomUUID().toString();
}
