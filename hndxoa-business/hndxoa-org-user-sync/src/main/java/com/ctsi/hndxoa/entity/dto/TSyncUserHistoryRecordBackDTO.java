package com.ctsi.hndxoa.entity.dto;

import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> 15433
 * @date : 2025/07/03/9:01
 * description:
 */
@Data
public class TSyncUserHistoryRecordBackDTO {

    // 来源系统（zwylz）
    private String sourceKey;

    // 回调key(批次ID)
    private String eventId;

    /**
     * 同步成功的用户Phone（,拼接）
     */
    private String phone;

    /**
     * 同步异常信息
     */
    private String syncMessage;

    /**
     * 同步响应状态码 （200:正常响应）
     */
    private String syncStatus;
}
