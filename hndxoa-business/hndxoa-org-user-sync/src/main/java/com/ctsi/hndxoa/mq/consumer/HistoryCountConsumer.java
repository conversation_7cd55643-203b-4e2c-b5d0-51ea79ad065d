package com.ctsi.hndxoa.mq.consumer;

import com.alibaba.fastjson.JSON;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ctsi.hndx.config.DbConst;
import com.ctsi.hndxoa.entity.TSyncOrgHistroyRecord;
import com.ctsi.hndxoa.entity.TSyncUserHistroyRecord;
import com.ctsi.hndxoa.mapper.TSyncOrgHistroyRecordMapper;
import com.ctsi.hndxoa.mapper.TSyncUserHistroyRecordMapper;
import com.ctsi.hndxoa.pull.entity.dto.CountMessage;
import com.ctsi.hndxoa.service.constant.OrgUserConstants;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

@Slf4j
@Component
public class HistoryCountConsumer {

    public static final String EXCHANGE = "HistoryCountQueue";
    public static final String QUEUE = "HistoryCountExchange";
    public static final String ROUTING_KEY = "HistoryCount";


    @Resource
    private TSyncOrgHistroyRecordMapper orgHistroyRecordMapper;

    @Resource
    private TSyncUserHistroyRecordMapper userHistroyRecordMapper;

    /**
     * 消费延迟消息，更新数据库
     */
    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = QUEUE, durable = "true"),
            exchange = @Exchange(value = EXCHANGE, type = "topic"),
            key = ROUTING_KEY))
    @DS(DbConst.SHARDING)
    @Transactional(rollbackFor = Exception.class)
    public void handleDelayMessage(String message) {
        try {
            // 1. 解析消息
            CountMessage msg = JSON.parseObject(message, CountMessage.class);
            int type = msg.getType();
            Long historyId = msg.getHistoryId();
            Long count = msg.getCount();

            // 2. 根据类型更新数据库
            if (OrgUserConstants.PushType.PUSH_UNIT == type) {
                LambdaUpdateWrapper<TSyncOrgHistroyRecord> orgHistoryLuw = new LambdaUpdateWrapper<>();
                orgHistoryLuw.set(TSyncOrgHistroyRecord::getSyncCount, count);
                orgHistoryLuw.eq(TSyncOrgHistroyRecord::getId, historyId);
                // 更新组织表：例如更新count字段
                orgHistroyRecordMapper.update(null, orgHistoryLuw);
            } else if (OrgUserConstants.PushType.PUSH_USER == type) {
                LambdaUpdateWrapper<TSyncUserHistroyRecord> userHistoryLuw = new LambdaUpdateWrapper<>();
                userHistoryLuw.set(TSyncUserHistroyRecord::getSyncCount, count);
                userHistoryLuw.eq(TSyncUserHistroyRecord::getId, historyId);
                // 更新用户表：例如更新count字段
                userHistroyRecordMapper.update(null, userHistoryLuw);
            }
            log.info("延迟更新数据库成功：{}", msg);
        } catch (Exception e) {
            // 处理异常（例如记录日志、重试）
            log.error("延迟更新数据库失败：{}", e.getMessage());
        }
    }
}