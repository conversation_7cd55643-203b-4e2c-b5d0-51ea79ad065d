package com.ctsi.hndxoa.service.event;

import cn.hutool.core.collection.CollectionUtil;
import com.ctsi.hndxoa.entity.dto.SyncOrgUserDTO;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.OrgPushUserEvent;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * <AUTHOR> 15433
 * @date : 2025/05/19/10:11
 * description:
 */
@Slf4j
@Component
public class OrgPushUserEventListener {

    @Autowired
    private CscpUserRepository cscpUserRepository;
    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    @Autowired
    private ITSyncAppSystemManageService itSyncAppSystemManageService;

    @EventListener
    public void onLoginEvent(OrgPushUserEvent orgPushUser) {
        if (null != orgPushUser) {
            if (CollectionUtil.isNotEmpty(orgPushUser.getCscpOrgMap())) {
                SecurityContext securityContext = SecurityContextHolder.getContext();
                Map<Long, CscpOrg> cscpOrgMap = orgPushUser.getCscpOrgMap();
                // 当机构排序发生变化，下级机构会发生变更，所以需要同步推送
                for (CscpOrg cscpOrg : cscpOrgMap.values()) {
                    CompletableFuture.runAsync(() -> {
                        SecurityContextHolder.setContext(securityContext);
                        pushOrgList(cscpOrg.getId());
                    });
                }
            }

            if (CollectionUtil.isNotEmpty(orgPushUser.getCscpOrgList())) {
                SecurityContext securityContext = SecurityContextHolder.getContext();

                List<CscpOrg> cscpOrgList = orgPushUser.getCscpOrgList();
                Map<Long, CscpOrg> cscpOrgMap = new HashMap<>();
                cscpOrgList.forEach(cscpOrg -> {
                    cscpOrgMap.putIfAbsent(cscpOrg.getId(), cscpOrg);
                });
                // 当机构发生变更，下级机构中存在的用户信息和关联关系，需要再次推送
                for (CscpOrg cscpOrg : cscpOrgMap.values()) {
                    CompletableFuture.runAsync(() -> {
                        SecurityContextHolder.setContext(securityContext);
                        Long cId = cscpOrg.getCompanyId();
                        CscpOrg com = cscpOrgRepository.selectById(cId);
                        if (com == null) {
                            com = cscpOrgRepository.selectById(cscpOrg.getId());
                            cId = com.getId();
                        }
                        // 用户单位调整
                        cscpUserRepository.updateUserCompanyId(cscpOrg.getId(), cId, com.getOrgName(), com.getOrgAbbreviation());
                        List<Long> userIds = cscpUserRepository.selectUserCompanyId(cscpOrg.getId(), cId);
                        // 推送
                        pushUserList(userIds);
                    });
                }
            }
        }

    }

    private void pushOrgList(Long orgId) {
        try {
            SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
            syncOrgUserDTO.setFlag("update");
            syncOrgUserDTO.setOrgId(orgId);
            // 调用自动推送
            itSyncAppSystemManageService.syncOrgBusiness(syncOrgUserDTO);
        } catch (Exception e) {
            log.error("同步业务失败，ID: {}", orgId, e);
        }
    }

    private void pushUserList(List<Long> userIds) {

        for (Long id : userIds) {
            try {
                SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                syncOrgUserDTO.setFlag("update");
                syncOrgUserDTO.setUserId(id);
                // 调用自动推送
                itSyncAppSystemManageService.syncUserBusiness(syncOrgUserDTO);
            } catch (Exception e) {
                log.error("同步业务失败，ID: {}", id, e);
            }
        }
    }
}
