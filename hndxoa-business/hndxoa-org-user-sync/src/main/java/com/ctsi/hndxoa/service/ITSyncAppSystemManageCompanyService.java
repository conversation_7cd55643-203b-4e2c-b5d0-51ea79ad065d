package com.ctsi.hndxoa.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndxoa.entity.TSyncAppSystemManageCompany;
import com.ctsi.hndxoa.entity.dto.TSyncAppSystemManageCompany.TSyncAppSystemAuthorizationHistoryDTO;
import com.ctsi.hndxoa.entity.dto.TSyncAppSystemManageCompany.TSyncAppSystemManageCompanyDTO;
import com.ctsi.hndxoa.entity.dto.TSyncAppSystemManageCompany.TSyncAppSystemManageCompanyPageDTO;
import com.ctsi.ssdc.model.PageResult;

import java.util.List;

public interface ITSyncAppSystemManageCompanyService extends SysBaseServiceI<TSyncAppSystemManageCompany> {

    /**
     * 根据orgId查询应用信息
     */
    PageResult<TSyncAppSystemManageCompanyPageDTO> getListByParam(Long orgId,TSyncAppSystemManageCompanyPageDTO record, BasePageForm basePageForm);

    /**
     * 添加单位授权应用
     * @param record
     */
    void save(TSyncAppSystemManageCompanyDTO record);

    /**
     * 修改单位授权应用
     * @param record
     */
    void update(TSyncAppSystemManageCompanyDTO record);

    /**
     * 移除单位应用授权
     * @param record
     */
    void remove(TSyncAppSystemManageCompanyDTO record);

    /**
     * 根据appId查询机构信息
     * @param id
     * @return
     */
    List<Long> getListByAppId(Long id);

    PageResult<TSyncAppSystemManageCompanyPageDTO> getAuthorizedAppsForCurrentUser(TSyncAppSystemManageCompanyPageDTO query, BasePageForm pageForm);

    PageResult<TSyncAppSystemAuthorizationHistoryDTO> getAuthorizationHistory(TSyncAppSystemManageCompany query, BasePageForm pageForm);

    void authorizeAppToRegions(Long appId, List<Long> regionOrgIds);
}
