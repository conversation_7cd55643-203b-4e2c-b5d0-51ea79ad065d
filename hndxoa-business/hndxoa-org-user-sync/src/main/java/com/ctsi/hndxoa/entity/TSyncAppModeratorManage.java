package com.ctsi.hndxoa.entity;

import com.baomidou.mybatisplus.annotation.TableName;
import com.ctsi.hndx.common.BaseEntity;
import java.io.Serializable;
import java.math.BigInteger;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <p>
 * 版主应用管理表
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-04
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("T_SYNC_APP_MODERATOR_MANAGE")
@ApiModel(value="TSyncAppModeratorManage对象", description="版主应用管理表")
public class TSyncAppModeratorManage extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 应用ID (关联 t_sync_app_system_manage.id)
     */
    @ApiModelProperty(value = "应用ID (关联 t_sync_app_system_manage.id)")
    private Long appId;

    /**
     * 版主用户ID (关联 cscp_user.id)
     */
    @ApiModelProperty(value = "版主用户ID (关联 cscp_user.id)")
    private Long moderatorId;

}
