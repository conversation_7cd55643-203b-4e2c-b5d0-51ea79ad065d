package com.ctsi.hndxoa.entity.dto;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * <p>
 * 死信队列持久化表
 * </p>
 *
 * <AUTHOR>
 */
@Data
@TableName("t_mq_dlx")
@ApiModel(value = "t_mq_dlx对象", description = "死信队列持久化表")
public class TMqDlx implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "id")
    private Long id;

    @ApiModelProperty(value = "business_queue")
    private String businessQueue;

    @ApiModelProperty(value = "message_id")
    private String messageId;

    @ApiModelProperty(value = "message_body")
    private String messageBody;

    @ApiModelProperty(value = "fail_reason")
    private String failReason;

    @ApiModelProperty(value = "retry_count")
    private Integer retryCount = 3;

    @ApiModelProperty(value = "status")
    private String status = "0";

    @ApiModelProperty(value = "create_time")
    private LocalDateTime createTime;

    @ApiModelProperty(value = "create_time")
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeStart;
    @ApiModelProperty(value = "create_time")
    @TableField(exist = false)
    @DateTimeFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTimeEnd;

}
