package com.ctsi.hndxoa.mq.consumer;


import com.alibaba.fastjson.JSONObject;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.entity.dto.SyncCommonHttpDTO;
import com.ctsi.hndxoa.entity.dto.SyncOrgUserDTO;
import com.ctsi.hndxoa.entity.dto.TMqDlx;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.rabbitmq.client.Channel;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class SyncCommonHttpConsumer {

    public static final String EXCHANGE = "syncCommonHttpExchange";
    public static final String QUEUE = "syncCommonHttpQueue";
    public static final String ROUTING_KEY = "http";
    public static final int RETRY_COUNT = 2;

    @Autowired
    private ITSyncAppSystemManageService tSyncAppSystemManageService;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private DlxMessageQueue dlxMessageQueue;

    private static final Logger log = LoggerFactory.getLogger(SyncCommonHttpConsumer.class);

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = QUEUE, durable = "true"),
            exchange = @Exchange(value = EXCHANGE, type = "topic"),
            key = ROUTING_KEY
    ), concurrency = "2")
    public void onMessage(String message, Channel channel, Message amqpMessage) {

        String retryKey = "retry_count:" + QUEUE + ":" + ROUTING_KEY + ":";
        try {
            JSONObject json = JSONObject.parseObject(message);
            JSONObject cscpOrgJson = json.getJSONObject("cscpOrg");
            JSONObject appSystemManage = json.getJSONObject("appSystemManage");
            JSONObject bodyParams = json.getJSONObject("bodyParams");
            JSONObject cscpUserDto = json.getJSONObject("cscpUserDto");
            JSONObject syncOrgUserDTO = json.getJSONObject("syncOrgUserDTO");
            JSONObject cscpUserDetail = json.getJSONObject("cscpUserDetail");
            String uuid = json.getString("uuid");
            retryKey = retryKey + uuid + ":";
            SyncCommonHttpDTO dto;
            try {
                dto = new SyncCommonHttpDTO();
                if (appSystemManage != null) {
                    dto.setAppSystemManage(appSystemManage.toJavaObject(TSyncAppSystemManage.class));
                }
                if (bodyParams != null) {
                    Map<String, Object> map = new HashMap<>(bodyParams);
                    dto.setBodyParams(map);
                }
                if (cscpOrgJson != null) {
                    dto.setCscpOrg(cscpOrgJson.toJavaObject(CscpOrg.class));
                }
                if (cscpUserDto != null) {
                    dto.setCscpUserDto(cscpUserDto.toJavaObject(CscpUserDTO.class));
                }
                if (syncOrgUserDTO != null) {
                    dto.setSyncOrgUserDTO(syncOrgUserDTO.toJavaObject(SyncOrgUserDTO.class));
                }
                if (cscpUserDetail != null) {
                    dto.setCscpUserDetail(cscpUserDetail.toJavaObject(CscpUserDetail.class));
                }
            } catch (Exception e) {
                log.error("SyncCommonHttpQueue Invalid message format: {}", message);
                return;
            }
            Boolean success = redisTemplate.opsForValue().setIfAbsent(retryKey + "flag", 0, 1, TimeUnit.HOURS);
            if (Boolean.FALSE.equals(success)) {
                log.error("消息已处理，跳过重复消费，message={}", message);
                return;
            }
            tSyncAppSystemManageService.commonHttpRequest(dto, true);
        } catch (Exception e) {
            Long rr = redisTemplate.opsForValue().increment(retryKey);
            redisTemplate.expire(retryKey, 1, TimeUnit.HOURS);
            long currentRetry = java.util.Optional.ofNullable(rr).orElse(1L);
            // 目前先设置每个消息消费可以失败两次
            if (currentRetry < RETRY_COUNT) {
                // 抛错重新入队
                redisTemplate.delete(retryKey + "flag");
                throw new BusinessException("SyncCommonHttpQueue消息处理失败，重新入队:{}", e);
            } else {
                log.error("消息处理失败，已达到最大重试次数 [{}次]，转发到死信队列", RETRY_COUNT, e);
                try {
                    // 记录
                    TMqDlx tMqDlx = new TMqDlx();
                    tMqDlx.setBusinessQueue(amqpMessage.getMessageProperties().getConsumerQueue());
                    tMqDlx.setMessageId(retryKey);
                    tMqDlx.setMessageBody(message);
                    tMqDlx.setFailReason(StringUtils.substring(e.toString(), 0, 500));
                    tMqDlx.setStatus("0");
                    tMqDlx.setCreateTime(LocalDateTime.now());
                    tMqDlx.setRetryCount(RETRY_COUNT);
                    dlxMessageQueue.add(tMqDlx);
                } catch (Exception ex) {
                    log.error("处理消息失败时发生异常, message={}, error={}", message, ex.toString());
                }
            }
        }
    }

}
