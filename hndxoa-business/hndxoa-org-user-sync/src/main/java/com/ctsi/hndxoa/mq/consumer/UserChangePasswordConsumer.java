package com.ctsi.hndxoa.mq.consumer;

import com.alibaba.fastjson.JSONObject;
import com.ctsi.hndx.westone.WestoneEncryptService;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.service.CscpUserService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class UserChangePasswordConsumer {

    @Autowired
    CscpUserService cscpUserService;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private WestoneEncryptService westoneEncryptService;

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = "userChangePasswordQueue", durable = "true"),
            exchange = @Exchange(value = "userChangePasswordExchange", type = "topic"),
            key = "userChangePassword"
    ))
    public void onMessage(String message, Message amqpMessage) {
        log.info("userChangePasswordQueue收到消息："+message);
        CscpUser cscpUserSfw= JSONObject.parseObject(message, CscpUser.class);
        if(StringUtils.isNotBlank(cscpUserSfw.getStrId())){
            CscpUser cscpUser = cscpUserService.selectUserByStrId(cscpUserSfw.getStrId());
            if(cscpUser==null){
                return;
            }
            if(cscpUser!=null&&StringUtils.isNotBlank(cscpUserSfw.getPassword())) {
                cscpUser.setPassword(passwordEncoder.encode(cscpUserSfw.getPassword()));

            }
            if(cscpUser!=null&&StringUtils.isNotBlank(cscpUserSfw.getStrIdCardNo())){
                String encStrIdCardNo = cscpUserSfw.getStrIdCardNo();
                if (westoneEncryptService.isCipherMachine() && (null != encStrIdCardNo && !"".equals(encStrIdCardNo))) {
                    encStrIdCardNo = westoneEncryptService.encryptBySM4ECB_WithKeyProtectedBySM2(cscpUserSfw.getStrIdCardNo());
                }
                cscpUser.setIdCardNo(encStrIdCardNo);
                cscpUser.setStrIdCardNo(encStrIdCardNo);
            }
            //更新性别
            if(
                    cscpUserSfw.getSex()!=null&&StringUtils.isBlank(cscpUserSfw.getPassword())&&StringUtils.isBlank(cscpUserSfw.getStrIdCardNo())){
                cscpUser.setSex(cscpUserSfw.getSex());
            }



            cscpUserService.updateById(cscpUser);
            cscpUserService.clearUserCache(cscpUser.getLoginName());
            if(cscpUserSfw.getMobile() != null){
                cscpUserService.clearUserCacheUsernameOrMobile(cscpUserSfw.getMobile());
            }
        }

    }
}
