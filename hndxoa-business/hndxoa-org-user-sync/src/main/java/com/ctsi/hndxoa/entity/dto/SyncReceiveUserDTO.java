package com.ctsi.hndxoa.entity.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 接收业务系统用户类
 */
@Data
@ApiModel(value="SyncReceiveUserDTO对象", description="接收业务系统用户类")
public class SyncReceiveUserDTO {

    /**
     * id
     */
    @ExcelProperty(value = "用户主键")
    private String id;

    /**
     * 登录名称
     */
    @ExcelProperty(value = "登录名称")
    private String loginName;

    /**
     * 真实名称
     */
    @ExcelProperty(value = "真实姓名")
    private String realName;

    /**
     * 性别
     */
    @ExcelProperty(value = "性别")
    private String sex;

    /**
     * 手机号码
     */
    @ExcelProperty(value = "手机号码")
    private String mobile;

    /**
     * 身份号
     */
    @ExcelProperty(value = "身份证号")
    private String idCardNo;

    /**
     * 组织机构id
     */
    @ExcelProperty(value = "组织机构主键")
    private String orgIds;

    /**
     * 用户排序
     */
    @ExcelProperty(value = "用户排序")
    private String orderBy;

    /**
     * 状态
     */
    @ExcelIgnore
    private String status;

    @ExcelProperty("失败原因")
    private String failedReason;

}
