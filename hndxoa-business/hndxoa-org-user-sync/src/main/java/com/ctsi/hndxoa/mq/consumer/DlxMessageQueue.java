package com.ctsi.hndxoa.mq.consumer;

/**
 * <AUTHOR> 15433
 * @date : 2025/04/09/10:57
 * description: 手搓死信队列
 */

import com.ctsi.hndxoa.entity.dto.TMqDlx;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;

@Component
public class DlxMessageQueue {

    private final BlockingQueue<TMqDlx> queue = new LinkedBlockingQueue<>();

    private static final Logger logger = LoggerFactory.getLogger(DlxMessageQueue.class);

    public void add(TMqDlx dlxMessage) {
        queue.add(dlxMessage);
    }

    public TMqDlx take() {
        try {
            logger.info("queue take before size:{}", queue.size());
            return queue.take();
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("Failed to take message from queue", e);
        }
    }

    public boolean isEmptyQueue() {
        return queue.isEmpty();
    }

    public TMqDlx poll() {
        return queue.poll();
    }
}