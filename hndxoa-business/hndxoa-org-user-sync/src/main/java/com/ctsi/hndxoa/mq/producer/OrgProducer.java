package com.ctsi.hndxoa.mq.producer;

import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class OrgProducer {

    private static final String EXCHANGE = "orgExchange";

    @Autowired
    private RabbitTemplate rabbitTemplate;

    @Autowired
    private ITSyncAppSystemManageService systemManageService;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    public void syncToAll(String userJson) {
        List<TSyncAppSystemManage> projects = systemManageService.getActiveProjectsOrg();
        for (TSyncAppSystemManage project : projects) {
            rabbitTemplate.convertAndSend(EXCHANGE, project.getAppRegionCode(cscpOrgRepository), userJson);
        }
    }

    public void syncToProject(String appCode, String userJson) {
        rabbitTemplate.convertAndSend(EXCHANGE, appCode, userJson);
    }
}