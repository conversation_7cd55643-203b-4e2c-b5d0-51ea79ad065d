package com.ctsi.hndxoa.entity.dto;

import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.mapper.TSyncAppSystemManageMapper;
import com.ctsi.ssdc.security.CscpUserDetail;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class SyncOrgUserDTO implements Serializable {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("应用Id")
    private Long appId;

    @ApiModelProperty("机构Id")
    private Long orgId;

    @ApiModelProperty("多个机构Id")
    private List<Long> orgIds;

    @ApiModelProperty("用户Id")
    private Long userId;

    @ApiModelProperty("标识")
    private String flag;

    @ApiModelProperty(value = "是否单位管理员，1-是，0-否")
    private String unitAdminFlag;

    @ApiModelProperty("用户信息")
    private CscpUserDetail cscpUserDetail;

    private String appName;

    private String strUnitName;

    private String loginName;

    private Integer inSystemFlag;

    private Long sourceId;

    /**
     * 0-手动推送，1-自动推送
     */
    @ApiModelProperty(value = "0-手动推送，1-自动推送")
    private String operateType;

    /**
     * 用于判断是否来源自动推送
     */
    private Boolean isAutoPushFlag = false;

    private TSyncAppSystemManage appSystemManage;

    private List<String> appCodes;

    @ApiModelProperty("新增的推送应用")
    private List<String> addAppCodes;
    @ApiModelProperty("更新的推送应用")
    private List<String> updateAppCodes;
    @ApiModelProperty("被取消授权的推送应用")
    private List<String> removeAppCodes;

    private String pushAppCode;

    public TSyncAppSystemManage getCompatibleApp(TSyncAppSystemManageMapper tSyncAppSystemManageMapper) {
        if (appSystemManage == null && appId != null) {
            return tSyncAppSystemManageMapper.selectById(appId);
        }
        return appSystemManage;
    }
}
