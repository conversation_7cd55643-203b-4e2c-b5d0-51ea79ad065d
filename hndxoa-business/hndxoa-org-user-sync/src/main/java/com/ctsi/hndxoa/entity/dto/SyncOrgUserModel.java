package com.ctsi.hndxoa.entity.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SyncOrgUserModel {

    /**
     * 公共字段
     */

    @ApiModelProperty("主键ID")
    private String strId;

    @ApiModelProperty("邮政编码")
    private String strPostalCode;

    /**
     * 同步单位信息请求字段
     */

    @ApiModelProperty("上级单位ID（-1:表示无上级）")
    private String strParentId;

    @ApiModelProperty("单位名称")
    private String strUnitName;

    @ApiModelProperty("创建时间")
    private String dtCreatDate;

    @ApiModelProperty("单位编码")
    private String strUnitCode;

    @ApiModelProperty("备注")
    private String strDescription;

    @ApiModelProperty("单位简称")
    private String strEasyName;

    @ApiModelProperty("单位地址")
    private String strUnitAddress;

    @ApiModelProperty("单位网址")
    private String strUnitNet;

    @ApiModelProperty("单位邮箱")
    private String strUnitEmail;

    @ApiModelProperty("区号")
    private String strAreaCode;

    @ApiModelProperty("单位电话")
    private String strUnitPhone;

    @ApiModelProperty("传真")
    private String strUnitFax;

    @ApiModelProperty("排序")
    private Integer intSort;

    @ApiModelProperty("单位联络人")
    private String strRelationUser;

    @ApiModelProperty("单位信任号")
    private String strTrustNo;

    @ApiModelProperty("上级信任号")
    private String strParentTrustNo;

    @ApiModelProperty(value = "卫士通统一身份认证系统组织机构ID")
    private String westoneOrgId;

    @ApiModelProperty(value = "卫士通统一身份认证系统上级组织机构ID")
    private String westoneOrgParentId;

    /**
     * 同步用户信息请求字段
     */

    @ApiModelProperty("用户ID")
    private String strUserId;

    @ApiModelProperty("用户密级（10:普通　20:秘密　30:机密）")
    private String strClassified;

    @ApiModelProperty("用户姓名")
    private String strCname;

    @ApiModelProperty("邮箱")
    private String strEmail;

    @ApiModelProperty("手机号码")
    private String strMobile;

    @ApiModelProperty("手机号码")
    private String strMobileOther;

    @ApiModelProperty("传真")
    private String strFax;

    @ApiModelProperty("办公电话")
    private String strOPhone;

    @ApiModelProperty("联系地址")
    private String strPosttalAddress;

    @ApiModelProperty("住宅号码")
    private String strHPhone;

    @ApiModelProperty("住宅地址")
    private String strHomeAdd;

    @ApiModelProperty("单位电话")
    private String strFlag;

    @ApiModelProperty("性别")
    private String strSex;

    @ApiModelProperty("MSN/QQ")
    private String strMsnQq;

    @ApiModelProperty("昵称")
    private String strNickName;

    @ApiModelProperty("职位")
    private String strDuty;

    @ApiModelProperty("岗位")
    private String strPost;

    @ApiModelProperty("电子认证号")
    private String strElecCAN;

    @ApiModelProperty("移动终端序列号")
    private String strMTSID;

    @ApiModelProperty("证件号")
    private String strIdCardNo;

    @ApiModelProperty("单位信任号")
    private String strUnitTrustNo;

    @ApiModelProperty(value = "卫士通统一身份认证系统组织机构ID")
    private String westoneUserId;

    @ApiModelProperty(value = "是否单位管理员，1-是，0-否")
    private String unitAdminFlag;
    @ApiModelProperty(value = "角色id")
    private String roleIdsStr;

    @ApiModelProperty("用户所属单位集合")
    private List<SyncUserUnitModel> units;
}
