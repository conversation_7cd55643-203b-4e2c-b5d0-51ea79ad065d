package com.ctsi.hndxoa.service.job;

import com.ctsi.hndxoa.entity.TSyncOrgHistroyRecord;
import com.ctsi.hndxoa.mapper.TSyncOrgHistroyRecordMapper;
import com.ctsi.hndxoa.service.ITSyncOrgHistroyRecordService;
import com.ctsi.hndxoa.service.constant.OrgUserConstants;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR> 15433
 * @date : 2025/03/03/10:40
 * description:
 */

@Service
@Slf4j
public class SyncOrgHistroyRecordJob {

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    @Autowired
    private TSyncOrgHistroyRecordMapper tSyncOrgHistroyRecordMapper;
    @Autowired
    private ITSyncOrgHistroyRecordService tSyncOrgHistroyRecordService;

//    @Scheduled(cron ="0 0/5 * * * ? ")
    @Transactional(rollbackFor = Exception.class)
    public void syncUpdateMeetingStatus() {

        log.info("==============批量更新机构同步结果开始==============");

        Map<Object, Object> valueMap = redisTemplate.opsForHash().entries(OrgUserConstants.RedisKey.SYNC_OGR_HISTORY_ID);

        if (MapUtils.isNotEmpty(valueMap)) {
            Map<Long, String> keyValueMap = new HashMap<>(valueMap.size());
            for (Map.Entry<Object, Object> entry : valueMap.entrySet()) {
                Object key = entry.getKey();
                keyValueMap.put(Long.parseLong(key.toString()), String.valueOf(entry.getValue()));
            }

            List<TSyncOrgHistroyRecord> tSyncOrgHistroyRecords = tSyncOrgHistroyRecordMapper.selectBatchIds(keyValueMap.keySet());
            if (CollectionUtils.isNotEmpty(tSyncOrgHistroyRecords)) {

                for (TSyncOrgHistroyRecord tSyncOrgHistroyRecord : tSyncOrgHistroyRecords) {
                    tSyncOrgHistroyRecord.setStrOperaType(keyValueMap.get(tSyncOrgHistroyRecord.getId()));
                    tSyncOrgHistroyRecord.setSyncSuccess("true");
                    tSyncOrgHistroyRecord.setSyncStatus("200");
                    tSyncOrgHistroyRecord.setSyncMessage("操作成功");
                }
                tSyncOrgHistroyRecordService.saveOrUpdateBatch(tSyncOrgHistroyRecords, tSyncOrgHistroyRecords.size());
            }
            for (Long key : keyValueMap.keySet()) {
                redisTemplate.opsForHash().delete(OrgUserConstants.RedisKey.SYNC_OGR_HISTORY_ID, key.toString());
            }
        }

        log.info("批量更新机构同步结果结束");

    }
}
