package com.ctsi.hndxoa.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndxoa.entity.dto.TSyncOrgHistroyRecordDTO;
import com.ctsi.hndxoa.service.ITSyncOrgHistroyRecordService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tSyncOrgHistroyRecord")
@Api(value = "同步机构历史记录表", tags = "同步机构历史记录表接口")
public class TSyncOrgHistroyRecordController extends BaseController {

    private static final String ENTITY_NAME = "tSyncOrgHistroyRecord";

    @Autowired
    private ITSyncOrgHistroyRecordService tSyncOrgHistroyRecordService;



    /**
     *  新增同步机构历史记录表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tSyncOrgHistroyRecord.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增同步机构历史记录表批量数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tSyncOrgHistroyRecord.add')")
    public ResultVO createBatch(@RequestBody List<TSyncOrgHistroyRecordDTO> tSyncOrgHistroyRecordList) {
       Boolean  result = tSyncOrgHistroyRecordService.insertBatch(tSyncOrgHistroyRecordList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  新增数据.
     */
    @PostMapping("/create")
    @ApiOperation(value = "新增(权限code码为：cscp.tSyncOrgHistroyRecord.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增同步机构历史记录表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tSyncOrgHistroyRecord.add')")
    public ResultVO<TSyncOrgHistroyRecordDTO> create(@RequestBody TSyncOrgHistroyRecordDTO tSyncOrgHistroyRecordDTO)  {
        TSyncOrgHistroyRecordDTO result = tSyncOrgHistroyRecordService.create(tSyncOrgHistroyRecordDTO);
        return ResultVO.success(result);
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tSyncOrgHistroyRecord.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新同步机构历史记录表数据")
    @PreAuthorize("@permissionService.hasPermi('cscp.tSyncOrgHistroyRecord.update')")
    public ResultVO update(@RequestBody TSyncOrgHistroyRecordDTO tSyncOrgHistroyRecordDTO) {
	    Assert.notNull(tSyncOrgHistroyRecordDTO.getId(), "general.IdNotNull");
        int count = tSyncOrgHistroyRecordService.update(tSyncOrgHistroyRecordDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除同步机构历史记录表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tSyncOrgHistroyRecord.delete)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tSyncOrgHistroyRecord.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = tSyncOrgHistroyRecordService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        TSyncOrgHistroyRecordDTO tSyncOrgHistroyRecordDTO = tSyncOrgHistroyRecordService.findOne(id);
        return ResultVO.success(tSyncOrgHistroyRecordDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTSyncOrgHistroyRecordPage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TSyncOrgHistroyRecordDTO>> queryTSyncOrgHistroyRecordPage(TSyncOrgHistroyRecordDTO tSyncOrgHistroyRecordDTO, BasePageForm basePageForm) {
        return ResultVO.success(tSyncOrgHistroyRecordService.queryListPage(tSyncOrgHistroyRecordDTO, basePageForm));
    }

    /**
     *  分页查询多条数据-授权历史记录
     */
    @GetMapping("/queryTSyncOrgHistroyRecordPageByRole")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TSyncOrgHistroyRecordDTO>> queryTSyncOrgHistroyRecordPageByRole(TSyncOrgHistroyRecordDTO tSyncOrgHistroyRecordDTO, BasePageForm basePageForm) {
        return ResultVO.success(tSyncOrgHistroyRecordService.queryListPageByRole(tSyncOrgHistroyRecordDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTSyncOrgHistroyRecord")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TSyncOrgHistroyRecordDTO>> queryTSyncOrgHistroyRecord(TSyncOrgHistroyRecordDTO tSyncOrgHistroyRecordDTO) {
       List<TSyncOrgHistroyRecordDTO> list = tSyncOrgHistroyRecordService.queryList(tSyncOrgHistroyRecordDTO);
       return ResultVO.success(new ResResult<TSyncOrgHistroyRecordDTO>(list));
   }

}
