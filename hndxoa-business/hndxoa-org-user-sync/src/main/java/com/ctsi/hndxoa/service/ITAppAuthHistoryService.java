package com.ctsi.hndxoa.service;

import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceI;
import com.ctsi.hndxoa.entity.TAppAuthHistory;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;

import java.util.List;

public interface ITAppAuthHistoryService extends SysBaseServiceI<TAppAuthHistory> {

    /**
     * 根据orgId查询应用信息
     */
    PageResult<TAppAuthHistory> pageHistory(TAppAuthHistory record, BasePageForm basePageForm);


    void saveAppAuthByUser(Long id, String loginName, List<Long> appIds);

    void saveAppAuthByUser2(Long userId, CscpUserDetail cscpUserDetail, List<String> appCodes);

    void saveAppAuthByUser3(Long id, String loginName, String pushAppCode);

    void saveAppAuthByOrg(Long orgId, String orgName, List<Long> appIds);

    void saveAppAuthByOrg2(Long orgId, CscpUserDetail cscpUserDetail, List<String> appCodes);

    void saveAppAuthByOrg3(Long orgId, String orgName, String pushAppCode);
}
