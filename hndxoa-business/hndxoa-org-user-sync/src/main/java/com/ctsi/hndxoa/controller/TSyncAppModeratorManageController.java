package com.ctsi.hndxoa.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndxoa.entity.dto.TSyncAppModeratorManageDTO;
import com.ctsi.hndxoa.entity.dto.TSyncAppSystemManageCompany.TSyncAppSystemManageCompanyDTO;
import com.ctsi.hndxoa.service.ITSyncAppModeratorManageService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.model.ResResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;

import java.util.List;


/**
 * <p>
 * 前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-07-04
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tSyncAppModeratorManage")
@Api(value = "版主应用管理表", tags = "版主应用管理表接口")
public class TSyncAppModeratorManageController extends BaseController {

    private static final String ENTITY_NAME = "tSyncAppModeratorManage";

    @Autowired
    private ITSyncAppModeratorManageService tSyncAppModeratorManageService;



    /**
     *  新增版主应用管理表批量数据.
     */
    @PostMapping("/createBatch")
    @ApiOperation(value = "新增批量(权限code码为：cscp.tSyncAppModeratorManage.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增版主应用管理表批量数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tSyncAppModeratorManage.add')")
    public ResultVO createBatch(@RequestBody List<TSyncAppModeratorManageDTO> tSyncAppModeratorManageList) {
       Boolean  result = tSyncAppModeratorManageService.insertBatch(tSyncAppModeratorManageList);
       if(result){
           return ResultVO.success();
       }else {
           return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
       }
    }

     /**
     *  admin授权版主独立管理应用.
     */
    @PostMapping("/assignModerator")
    @ApiOperation(value = "新增(权限code码为：cscp.tSyncAppModeratorManage.add)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.ADD,message = "新增版主应用管理表数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tSyncAppModeratorManage.add')")
    public ResultVO<TSyncAppModeratorManageDTO> assignModerator(@RequestBody TSyncAppSystemManageCompanyDTO tSyncAppModeratorManageDTO)  {
        //TSyncAppModeratorManageDTO result = tSyncAppModeratorManageService.assignModerator(tSyncAppModeratorManageDTO);
        return ResultVO.success();
    }

    /**
     *  更新存在数据.
     */
    @PostMapping("/update")
    @ApiOperation(value = "更新存在数据(权限code码为：cscp.tSyncAppModeratorManage.update)", notes = "传入参数")
    @OperationLog(dBOperation = DBOperation.UPDATE,message = "更新版主应用管理表数据")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tSyncAppModeratorManage.update')")
    public ResultVO update(@RequestBody TSyncAppModeratorManageDTO tSyncAppModeratorManageDTO) {
	    Assert.notNull(tSyncAppModeratorManageDTO.getId(), "general.IdNotNull");
        int count = tSyncAppModeratorManageService.update(tSyncAppModeratorManageDTO);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

     /**
     *  删除存在数据.
     */
    @DeleteMapping("/delete/{id}")
    @OperationLog(dBOperation = DBOperation.DELETE,message = "删除版主应用管理表数据")
    @ApiOperation(value = "删除存在数据(权限code码为：cscp.tSyncAppModeratorManage.delete)", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tSyncAppModeratorManage.delete')")
    public ResultVO delete(@PathVariable Long id) {
        int count = tSyncAppModeratorManageService.delete(id);
        if(count > 0 ){
            return ResultVO.success();
        }else {
            return ResultVO.error(ResultCode.PARAM_NOT_UPDATE_DELETE);
        }
    }

    /**
     * 查询单条数据.
     */
    @GetMapping("/get/{id}")
    @ApiOperation(value = "查询单条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO get(@PathVariable Long id) {
        TSyncAppModeratorManageDTO tSyncAppModeratorManageDTO = tSyncAppModeratorManageService.findOne(id);
        return ResultVO.success(tSyncAppModeratorManageDTO);
    }

    /**
    *  分页查询多条数据.
    */
    @GetMapping("/queryTSyncAppModeratorManagePage")
    @ApiOperation(value = "翻页查询多条数据", notes = "传入参数")
    //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
    public ResultVO<PageResult<TSyncAppModeratorManageDTO>> queryTSyncAppModeratorManagePage(TSyncAppModeratorManageDTO tSyncAppModeratorManageDTO, BasePageForm basePageForm) {
        return ResultVO.success(tSyncAppModeratorManageService.queryListPage(tSyncAppModeratorManageDTO, basePageForm));
    }

   /**
    * 查询多条数据.不分页
    */
   @GetMapping("/queryTSyncAppModeratorManage")
   @ApiOperation(value = "查询多条数据", notes = "传入参数")
   //@PreAuthorize("@permissionService.hasPermi('cscp.tenant.edit')")
   public ResultVO<ResResult<TSyncAppModeratorManageDTO>> queryTSyncAppModeratorManage(TSyncAppModeratorManageDTO tSyncAppModeratorManageDTO) {
       List<TSyncAppModeratorManageDTO> list = tSyncAppModeratorManageService.queryList(tSyncAppModeratorManageDTO);
       return ResultVO.success(new ResResult<TSyncAppModeratorManageDTO>(list));
   }

}
