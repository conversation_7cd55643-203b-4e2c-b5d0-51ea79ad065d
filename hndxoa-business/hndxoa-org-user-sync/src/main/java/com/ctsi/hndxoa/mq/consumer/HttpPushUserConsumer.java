package com.ctsi.hndxoa.mq.consumer;


import com.alibaba.fastjson.JSONObject;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.DateUtils;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.entity.dto.SyncOrgUserDTO;
import com.ctsi.hndxoa.entity.dto.TMqDlx;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.ssdc.security.CscpUserDetail;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.annotation.Exchange;
import org.springframework.amqp.rabbit.annotation.Queue;
import org.springframework.amqp.rabbit.annotation.QueueBinding;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 */
@Component
public class HttpPushUserConsumer {

    public static final String EXCHANGE = "httpPushUserExchange";
    public static final String LZ_EXCHANGE = "httpPushUserExchange_lz";
    public static final String QUEUE = "httpPushUserQueue";
    public static final String LZ_QUEUE = "httpPushUserQueue_lz";
    public static final String ROUTING_KEY = "user";
    public static final String LZ_ROUTING_KEY = "zwylz";
    public static final int RETRY_COUNT = 2;

    @Autowired
    private ITSyncAppSystemManageService tSyncAppSystemManageService;
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    @Autowired
    private DlxMessageQueue dlxMessageQueue;

    private static final Logger log = LoggerFactory.getLogger(HttpPushUserConsumer.class);

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = QUEUE, durable = "true"),
            exchange = @Exchange(value = EXCHANGE, type = "topic"),
            key = ROUTING_KEY
    ), concurrency = "2")
    public void onMessage(String message, Message amqpMessage) {

        String retryKey = EXCHANGE + QUEUE + ROUTING_KEY + ":";
        try {
            JSONObject json = JSONObject.parseObject(message);
            JSONObject cscpUserDetail = json.getJSONObject("cscpUserDetail");
            JSONObject appSystemManage = json.getJSONObject("appSystemManage");
            String addAppCodes = json.getString("addAppCodes");
            String updateAppCodes = json.getString("updateAppCodes");
            String removeAppCodes = json.getString("removeAppCodes");
            Long userId = json.getLong("userId");
            String uuid = json.getString("uuid");
            Boolean isAutoPushFlag = json.getBoolean("isAutoPushFlag");
            String flag = json.getString("flag");
            String pushAppCode = json.getString("pushAppCode");
            retryKey = retryKey + uuid;
            SyncOrgUserDTO dto = new SyncOrgUserDTO();
            try {
                if (appSystemManage != null) {
                    dto.setAppSystemManage(appSystemManage.toJavaObject(TSyncAppSystemManage.class));
                }
                if (cscpUserDetail != null) {
                    dto.setCscpUserDetail(cscpUserDetail.toJavaObject(CscpUserDetail.class));
                }
                if (userId != null) {
                    dto.setUserId(userId);
                }
                if (isAutoPushFlag != null) {
                    dto.setIsAutoPushFlag(isAutoPushFlag);
                }
                if (flag != null) {
                    dto.setFlag(flag);
                }
                if (StringUtils.isNotEmpty(addAppCodes)) {
                    dto.setAddAppCodes(Arrays.asList(addAppCodes.split(",")));
                }
                if (StringUtils.isNotEmpty(updateAppCodes)) {
                    dto.setUpdateAppCodes(Arrays.asList(updateAppCodes.split(",")));
                }
                if (StringUtils.isNotEmpty(removeAppCodes)) {
                    dto.setRemoveAppCodes(Arrays.asList(removeAppCodes.split(",")));
                }
                if (pushAppCode != null) {
                    dto.setPushAppCode(pushAppCode);
                }
            } catch (Exception e) {
                log.error("HttpPushUserConsumer Invalid message format: {}", message);
                return;
            }
            Boolean success = redisTemplate.opsForValue().setIfAbsent(retryKey + ":flag", 0, 5, TimeUnit.MINUTES);
            if (Boolean.FALSE.equals(success)) {
                log.error("消息已处理，跳过重复消费，message={}", message);
                return;
            }
            tSyncAppSystemManageService.syncUserNew(dto);
        } catch (Exception e) {
            Long rr = redisTemplate.opsForValue().increment(retryKey);
            redisTemplate.expire(retryKey, 1, TimeUnit.HOURS);
            long currentRetry = java.util.Optional.ofNullable(rr).orElse(1L);
            // 目前先设置每个消息消费可以失败1次,最多消费两次
            if (currentRetry < RETRY_COUNT) {
                // 抛错重新入队
                redisTemplate.delete(retryKey + "flag");
                throw new RuntimeException("SyncCommonHttpQueue消息处理失败，重新入队:{}", e);
            } else {
                log.error("消息处理失败，已达到最大重试次数 [{}次]，转发到死信队列", RETRY_COUNT, e);
                try {
                    // 记录
                    TMqDlx tMqDlx = new TMqDlx();
                    tMqDlx.setBusinessQueue(amqpMessage.getMessageProperties().getConsumerQueue());
                    tMqDlx.setMessageId(retryKey);
                    tMqDlx.setMessageBody(message);
                    tMqDlx.setFailReason(StringUtils.substring(e.toString(), 0, 500));
                    tMqDlx.setStatus("0");
                    tMqDlx.setCreateTime(LocalDateTime.now());
                    tMqDlx.setRetryCount(RETRY_COUNT);
                    dlxMessageQueue.add(tMqDlx);
                } catch (Exception ex) {
                    log.error("处理消息失败时发生异常, message={}, error={}", message, ex.toString());
                }
            }
        }
    }

    @RabbitListener(bindings = @QueueBinding(
            value = @Queue(value = LZ_QUEUE, durable = "true"),
            exchange = @Exchange(value = LZ_EXCHANGE, type = "topic"),
            key = "${rabbitmq.lz-routingKey:none}"
    ))
    public void onLzMessage(String message, Message amqpMessage) {

        String retryKey = LZ_EXCHANGE + LZ_QUEUE + LZ_ROUTING_KEY + ":";
        try {
            JSONObject json = JSONObject.parseObject(message);
            JSONObject cscpUserDetail = json.getJSONObject("cscpUserDetail");
            JSONObject appSystemManage = json.getJSONObject("appSystemManage");
            String appCodes = json.getString("appCodes");
            Long userId = json.getLong("userId");
            String uuid = json.getString("uuid");
            Boolean isAutoPushFlag = json.getBoolean("isAutoPushFlag");
            String flag = json.getString("flag");
            String pushAppCode = json.getString("pushAppCode");
            retryKey = retryKey + uuid;
            SyncOrgUserDTO dto = new SyncOrgUserDTO();
            try {
                if (appSystemManage != null) {
                    dto.setAppSystemManage(appSystemManage.toJavaObject(TSyncAppSystemManage.class));
                }
                if (cscpUserDetail != null) {
                    dto.setCscpUserDetail(cscpUserDetail.toJavaObject(CscpUserDetail.class));
                }
                if (userId != null) {
                    dto.setUserId(userId);
                }
                if (isAutoPushFlag != null) {
                    dto.setIsAutoPushFlag(isAutoPushFlag);
                }
                if (flag != null) {
                    dto.setFlag(flag);
                }
                if (StringUtils.isNotEmpty(appCodes)) {
                    dto.setAppCodes(Arrays.asList(appCodes.split(",")));
                }
                if (pushAppCode != null) {
                    dto.setPushAppCode(pushAppCode);
                }
            } catch (Exception e) {
                log.error("HttpPushUserConsumer Invalid message format: {}", message);
                return;
            }
            Boolean success = redisTemplate.opsForValue().setIfAbsent(retryKey + ":flag", 0, 5, TimeUnit.MINUTES);
            if (Boolean.FALSE.equals(success)) {
                log.error("消息已处理，跳过重复消费，message={}", message);
                return;
            }
            // 加个分布式锁
            consumerLz(dto);
        } catch (Exception e) {
            log.error("量子消息处理失败，转发到死信队列", e);
            try {
                // 记录
                TMqDlx tMqDlx = new TMqDlx();
                tMqDlx.setBusinessQueue(amqpMessage.getMessageProperties().getConsumerQueue());
                tMqDlx.setMessageId(retryKey);
                tMqDlx.setMessageBody(message);
                tMqDlx.setFailReason(StringUtils.substring(e.toString(), 0, 500));
                tMqDlx.setStatus("0");
                tMqDlx.setCreateTime(LocalDateTime.now());
                tMqDlx.setRetryCount(1);
                dlxMessageQueue.add(tMqDlx);
            } catch (Exception ex) {
                log.error("处理消息失败时发生异常, message={}, error={}", message, ex.toString());
            }
        }
    }

    private void consumerLz(SyncOrgUserDTO dto) throws InterruptedException {
            String key = LZ_QUEUE + ":" + LZ_ROUTING_KEY;
            int retryCount = 0;

            while (retryCount < 50) {
                Boolean isLocked = redisTemplate.opsForValue().setIfAbsent(key, 0, 3100, TimeUnit.MILLISECONDS);
                if (Boolean.TRUE.equals(isLocked)) {
                    log.info("HttpPushUserConsumer onLzMessage 消息消费时间：{}", DateUtils.getCurrentDateTime());
                    tSyncAppSystemManageService.syncUserLz(dto);
                    return;
                } else {
                    retryCount++;
                    log.error("获取锁失败，第 {} 次重试...", retryCount);
                    Thread.sleep(3100);
                }
            }

            log.error("超过最大重试次数，消息处理失败: {}", dto);
        }

}
