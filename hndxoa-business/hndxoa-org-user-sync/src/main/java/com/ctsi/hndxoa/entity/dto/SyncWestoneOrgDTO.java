package com.ctsi.hndxoa.entity.dto;

import com.ctsi.hndx.common.BaseDtoEntity;
import lombok.*;

import java.util.List;

/**
 * <p>
 * 卫士通统一身份认证系统；同步组织机构数据查询DTO
 * </p>
 *
 * <AUTHOR>
 * @since 2024-12-30
 *
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
public class SyncWestoneOrgDTO extends BaseDtoEntity {

    private int type;

    private String orgName;

    private String orgCode;

    private Long parentId;

    private String westoneOrgId;

    private String westoneOrgParentId;

    private String regionCode;

    private List<SyncWestoneOrgDTO> children;

}
