package com.ctsi.hndxoa.controller;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.enums.DBOperation;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndxoa.entity.TSyncAppSystemManageCompany;
import com.ctsi.hndxoa.entity.dto.TSyncAppSystemManageCompany.TSyncAppSystemAuthorizationHistoryDTO;
import com.ctsi.hndxoa.entity.dto.TSyncAppSystemManageCompany.TSyncAppSystemManageCompanyDTO;
import com.ctsi.hndxoa.entity.dto.TSyncAppSystemManageCompany.TSyncAppSystemManageCompanyPageDTO;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageCompanyService;
import com.ctsi.ssdc.annotation.OperationLog;
import com.ctsi.ssdc.model.PageResult;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <p>
 * 应用授权市县区单位
 * </p>
 *
 * <AUTHOR>
 * @since 2024-08-27
 *
 */

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/tSyncAppSystemManage/cityUnit")
@Api(value = "应用授权市县区单位表", tags = "应用授权市县区单位接口")
public class TSyncAppSystemManageCompanyController extends BaseController {

    @Autowired
    private ITSyncAppSystemManageCompanyService syncAppSystemManageCompanyService;

    /**
     * 区划授权管理-列表分页
     * @param orgId
     * @param record
     * @param basePageForm
     * @return
     */
    @GetMapping("/getListByParam")
    public ResultVO<PageResult<TSyncAppSystemManageCompanyPageDTO>> getListByParam(Long orgId,TSyncAppSystemManageCompanyPageDTO record, BasePageForm basePageForm) {
        PageResult<TSyncAppSystemManageCompanyPageDTO> result = syncAppSystemManageCompanyService.getListByParam(orgId, record, basePageForm);
        return ResultVO.success(result);
    }

    /**
     * 区划授权管理-新增
     * @param record
     * @return
     */
    @PostMapping("/saveTSyncAppSystemManage")
    @OperationLog(dBOperation = DBOperation.ADD, message = "新增区划授权")
    public ResultVO<TSyncAppSystemManageCompanyDTO> saveTSyncAppSystemManage(@RequestBody TSyncAppSystemManageCompanyDTO record) {
        syncAppSystemManageCompanyService.save(record);
        return ResultVO.success();
    }

    /**
     * 区划授权管理-变更
     * @param record
     * @return
     */
    @PutMapping("/updateTSyncAppSystemManage")
    @OperationLog(dBOperation = DBOperation.UPDATE, message = "更新区划授权")
    public ResultVO<TSyncAppSystemManageCompanyDTO> updateTSyncAppSystemManage(@RequestBody TSyncAppSystemManageCompanyDTO record) {
        syncAppSystemManageCompanyService.update(record);
        return ResultVO.success();
    }

    /**
     * 区划授权管理-删除
     * @param record
     * @return
     */
    @DeleteMapping("/removeTSyncAppSystemManage")
    @OperationLog(dBOperation = DBOperation.DELETE, message = "删除区划授权")
    public ResultVO<TSyncAppSystemManageCompanyDTO> removeTSyncAppSystemManage(@RequestBody TSyncAppSystemManageCompanyDTO record) {
        syncAppSystemManageCompanyService.remove(record);
        return ResultVO.success();
    }

    /**
     * 区划授权管理-查询单个应该中勾选机构
     * @param id
     * @return
     */
    @GetMapping("/getListByAppId/{id}")
    public ResultVO<List<Long>> getListByAppId(@PathVariable Long id) {
        List<Long> dataList = syncAppSystemManageCompanyService.getListByAppId(id);
        return ResultVO.success(dataList);
    }

    /**
     * <AUTHOR>
     * @description 获取应用授权列表
     * - 区划管理员: 返回所有应用，并根据orgId标识授权状态
     * - 单位管理员: 返回已授权和自动推送的应用
     */
    @GetMapping("/getAuthorizedAppsForCurrentUser")
    @ApiOperation(value = "获取应用授权列表", notes = "根据当前用户角色（区划/单位管理员）获取应用授权列表")
    public ResultVO<PageResult<TSyncAppSystemManageCompanyPageDTO>> getAuthorizedApps(
            TSyncAppSystemManageCompanyPageDTO query,
            BasePageForm pageForm) {

        PageResult<TSyncAppSystemManageCompanyPageDTO> pageResult =
                syncAppSystemManageCompanyService.getAuthorizedAppsForCurrentUser( query, pageForm);

        return ResultVO.success(pageResult);
    }


    /**
     * @description 应用所有者（版主）将应用授权给指定的区划
     */
    @ApiOperation(value = "授权应用给区划", notes = "应用所有者（版主）将自己管理的应用授权给一个或多个区划")
    @PostMapping("/authorizeToRegion/{appId}")
    public ResultVO<String> authorizeAppToRegions(
            @ApiParam(value = "要授权的应用ID", required = true) @PathVariable Long appId,
            @ApiParam(value = "要授权的区划机构ID列表", required = true) @RequestBody List<Long> regionOrgIds) {

        syncAppSystemManageCompanyService.authorizeAppToRegions(appId, regionOrgIds);
        return ResultVO.success("授权成功");
    }

    /**
     * @description 获取授权历史记录列表
     */
    @ApiOperation(value = "获取授权历史记录", notes = "分页查询所有授权记录，包括已取消授权的记录，按授权时间降序排序")
    @GetMapping("/getAuthorizedHistoryPage")
    public ResultVO<PageResult<TSyncAppSystemAuthorizationHistoryDTO>> getAuthorizationHistory(
            TSyncAppSystemManageCompany query,
            BasePageForm pageForm) {

        PageResult<TSyncAppSystemAuthorizationHistoryDTO> pageResult =
                syncAppSystemManageCompanyService.getAuthorizationHistory(query, pageForm);

        return ResultVO.success(pageResult);
    }

}
