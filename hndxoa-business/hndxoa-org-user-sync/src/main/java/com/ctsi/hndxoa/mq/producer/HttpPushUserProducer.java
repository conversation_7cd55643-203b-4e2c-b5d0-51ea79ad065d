package com.ctsi.hndxoa.mq.producer;

import com.alibaba.fastjson.JSONObject;
import com.ctsi.hndxoa.entity.dto.HttpPushUserDTO;
import com.ctsi.hndxoa.mq.consumer.HttpPushUserConsumer;
import org.springframework.amqp.rabbit.core.RabbitTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Service
public class HttpPushUserProducer {

    @Autowired
    private RabbitTemplate rabbitTemplate;

    public void syncToProject(String exchange, String appCode, String userJson) {
        rabbitTemplate.convertAndSend(exchange, appCode, userJson);
    }

    public void httpPushUserToQueue(String userJson) {
        rabbitTemplate.convertAndSend(HttpPushUserConsumer.EXCHANGE, HttpPushUserConsumer.ROUTING_KEY, userJson);
    }

    public void httpPushUserToLzQueue(String userJson) {
        rabbitTemplate.convertAndSend(HttpPushUserConsumer.LZ_EXCHANGE, HttpPushUserConsumer.LZ_ROUTING_KEY, userJson);
    }

    public void httpPushUser(HttpPushUserDTO httpPushUserDTO) {
        if (httpPushUserDTO == null || httpPushUserDTO.getAppSystemManage() == null) {
           return;
        }
        String message = JSONObject.toJSONString(httpPushUserDTO);
        if (HttpPushUserConsumer.LZ_ROUTING_KEY.equals(httpPushUserDTO.getAppSystemManage().getAppCode())) {
            httpPushUserToLzQueue(message);
        } else {
            httpPushUserToQueue(message);
        }
    }
}
