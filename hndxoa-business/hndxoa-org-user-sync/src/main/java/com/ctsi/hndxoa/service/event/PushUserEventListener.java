package com.ctsi.hndxoa.service.event;

import com.ctsi.hndxoa.entity.dto.SyncOrgUserDTO;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.ssdc.admin.domain.dto.PushUserEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR> 15433
 * @date : 2025/05/19/10:11
 * description:
 */
@Slf4j
@Component
public class PushUserEventListener {
    @Autowired
    private ITSyncAppSystemManageService itSyncAppSystemManageService;

    @EventListener
    public void onLoginEvent(PushUserEvent pushUser) {

        if (pushUser != null && pushUser.getUserIdList() != null) {
            pushUserList(pushUser.getUserIdList());
        }
    }

    private void pushUserList(List<Long> userIds) {

        for (Long id : userIds) {
            try {
                SyncOrgUserDTO syncOrgUserDTO = new SyncOrgUserDTO();
                syncOrgUserDTO.setFlag("update");
                syncOrgUserDTO.setUserId(id);
                // 调用自动推送
                itSyncAppSystemManageService.syncUserBusiness(syncOrgUserDTO);
            } catch (Exception e) {
                log.error("同步业务失败，ID: {}", id, e);
            }
        }
    }
}
