package com.ctsi.hndxoa.mapper;

import com.baomidou.mybatisplus.annotation.InterceptorIgnore;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.ctsi.hndx.common.MybatisBaseMapper;
import com.ctsi.hndxoa.entity.TSyncAppSystemManageCompany;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <p>
 * 应用授权市县区单位 Mapper 接口
 * </p>
 */
@Mapper
public interface TSyncAppSystemManageCompanyMapper extends MybatisBaseMapper<TSyncAppSystemManageCompany> {

    @InterceptorIgnore(tenantLine="true")
    List<Long> getListByAppId(@Param("appId") Long appId, @Param("orgId") Long orgId);

    /**
     * @description 分页查询授权历史记录（包含已删除的）
     * @param page 分页对象
     * @param queryWrapper 查询条件
     * @return 分页结果
     */
    @Select("SELECT * FROM t_sync_app_system_manage_company ${ew.customSqlSegment}")
    IPage<TSyncAppSystemManageCompany> selectHistoryPage(IPage<TSyncAppSystemManageCompany> page,
                                                         @Param(Constants.WRAPPER) Wrapper<TSyncAppSystemManageCompany> queryWrapper);

    /**
     * 批量查询已存在的orgId和appId组合
     * @param dataList 分批次集合
     * @return 已存在的组合列表
     */
    List<TSyncAppSystemManageCompany> selectExistingOrgAppPairs(@Param("dataList") List<TSyncAppSystemManageCompany> dataList);

    /**
     * 批量插入记录
     * @param entities 待插入的实体列表
     */
    void batchInsert(@Param("dataList") List<TSyncAppSystemManageCompany> entities);
}
