package com.ctsi.hndxoa.mq;

import com.ctsi.hndx.annotations.ResponseResultVo;
import com.ctsi.hndx.common.BaseController;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.result.ResultVO;
import com.ctsi.hndxoa.entity.dto.SyncOrgUserBatchDTO;
import com.ctsi.hndxoa.entity.dto.SyncOrgUserDTO;
import com.ctsi.hndxoa.mq.producer.UserProducer;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.hndxoa.service.constant.OrgUserConstants;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@Slf4j
@RestController
@ResponseResultVo
@RequestMapping("/api/mq")
@Api(value = "MQ同步推送", tags = "MQ同步推送接口")
public class ProducerController extends BaseController {

    @Autowired
    UserProducer userProducer;

    @Autowired
    ITSyncAppSystemManageService itSyncAppSystemManageService;

    @PostMapping("/sendUserMessage")
    @ApiOperation(value = "推送用户-单个业务系统", notes = "传入参数")
    public ResultVO<String> sendUserMessage(@RequestBody SyncOrgUserDTO syncOrgUser) {
        itSyncAppSystemManageService.mqSyncUser(syncOrgUser);
        return ResultVO.success();
    }

    @PostMapping("/sendUserBatchMessage")
    @ApiOperation(value = "批量推送用户-单个业务系统", notes = "传入参数")
    public ResultVO<String> sendUserBatchMessage(@RequestBody SyncOrgUserBatchDTO syncOrgUserBatchDTO) {
        List<Long> userIds = syncOrgUserBatchDTO.getUserIds();
        userIds.forEach(u -> {
            SyncOrgUserDTO syncOrgUser = new SyncOrgUserDTO();
            syncOrgUser.setUserId(u);
            syncOrgUser.setAppId(syncOrgUserBatchDTO.getAppId());
            itSyncAppSystemManageService.mqSyncUser(syncOrgUser);
        });
        return ResultVO.success();
    }

    @PostMapping("/sendUserMessageDataBaseAll")
    @ApiOperation(value = "推送用户-所有业务系统", notes = "传入参数")
    public ResultVO<String> sendUserMessageDataBaseAll(@RequestBody SyncOrgUserDTO syncOrgUser) {
        itSyncAppSystemManageService.mqSyncUser(syncOrgUser);
        return ResultVO.success();
    }



    @PostMapping("/sendOrgMessage")
    @ApiOperation(value = "推送单个机构-单个应用", notes = "传入参数")
    public ResultVO<String> sendOrgMessage(@RequestBody SyncOrgUserDTO syncOrgUser) {
        Pair<String, String> result = itSyncAppSystemManageService.mqSyncOrg(syncOrgUser);
        return getSendResult(result);
    }

    @PostMapping("/sendOrgBatchMessage")
    @ApiOperation(value = "批量推送机构-单个业务系统", notes = "传入参数")
    public ResultVO<String> sendOrgBatchMessage(@RequestBody SyncOrgUserBatchDTO syncOrgUserBatchDTO) {
        List<Long> orgIds = syncOrgUserBatchDTO.getOrgIds();
        orgIds.forEach(u -> {
            SyncOrgUserDTO syncOrgUser = new SyncOrgUserDTO();
            syncOrgUser.setOrgId(u);
            syncOrgUser.setAppId(syncOrgUserBatchDTO.getAppId());
            itSyncAppSystemManageService.mqSyncOrg(syncOrgUser);
        });
        return ResultVO.success();
    }


    @PostMapping("/sendOrgMessageAll")
    @ApiOperation(value = "推送全部机构-单个应用", notes = "传入参数")
    public ResultVO<String> sendOrgMessageDataBaseAll(@RequestBody SyncOrgUserDTO syncOrgUser) {
        syncOrgUser.setOrgId(null);
        Pair<String, String> result = itSyncAppSystemManageService.mqSyncOrg(syncOrgUser);
        return getSendResult(result);
    }

    private ResultVO<String> getSendResult(Pair<String, String> result) {
        if (OrgUserConstants.SyncStatus.S.equals(result.getKey())) {
            return ResultVO.success();
        } else if (OrgUserConstants.SyncStatus.P.equals(result.getKey())) {
            return ResultVO.error(ResultCode.SYNC_DATA_PARTIAL.message(), result.getValue());
        } else {
            return ResultVO.error(ResultCode.SYNC_DATA_FAIL.message(), result.getValue());
        }
    }

    @PostMapping("/syncUserBatchByCompanyMessage")
    @ApiOperation(value = "批量推送指定单位id下所有用户-单个业务系统", notes = "传入参数")
    public ResultVO<String> syncUserBatchByCompany(@RequestBody SyncOrgUserDTO syncOrgUserDTO) {
        List<Long> userIds = itSyncAppSystemManageService.getAllUserByCompanyId(syncOrgUserDTO);
        SyncOrgUserBatchDTO userBatchDTO = new SyncOrgUserBatchDTO();
        userBatchDTO.setAppId(syncOrgUserDTO.getAppId());
        userBatchDTO.setUserIds(userIds);
        return this.sendUserBatchMessage(userBatchDTO);
    }
}
