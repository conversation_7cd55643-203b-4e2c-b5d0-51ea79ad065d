package com.ctsi.hndxoa.service;

import com.ctsi.hndxoa.entity.dto.SyncReceiveUserDTO;

import java.util.List;

/**
 *  接受推送
 *  常德协同办公机构
 */
public interface ISyncReceiveUserService {

    /**
     * 用户批处理
     * @param dataList
     * @return
     */
    String saveBatchUser(List<SyncReceiveUserDTO> dataList, String source);

    /**
     * 同步用户
     * @param data
     */
    void syncUser(SyncReceiveUserDTO data);

}
