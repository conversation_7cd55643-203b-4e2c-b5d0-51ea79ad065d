package com.ctsi.hndxoa.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndxoa.entity.dto.TMqDlx;
import com.ctsi.hndxoa.mapper.TMqDlxMapper;
import com.ctsi.hndxoa.service.ITMqDlxService;
import com.ctsi.ssdc.model.PageResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.amqp.core.Message;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <p>
 * 死信队列 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-03-12
 */
@Slf4j
@Service
public class ITMqDlxServiceImpl extends SysBaseServiceImpl<TMqDlxMapper, TMqDlx> implements ITMqDlxService {

    @Autowired
    private TMqDlxMapper tMqDlxMapper;

    @Override
    public void saveDlx(Message amqpMessage, String message, String errorMessage) {
        String messageId = amqpMessage.getMessageProperties().getMessageId();
        if (messageId == null) {
            messageId = amqpMessage.getMessageProperties().getConsumerTag();
        }
        TMqDlx tMqDlx = new TMqDlx();
        tMqDlx.setBusinessQueue(amqpMessage.getMessageProperties().getConsumerQueue());
        tMqDlx.setMessageId(messageId);
        tMqDlx.setMessageBody(message);
        tMqDlx.setFailReason(errorMessage);
        tMqDlx.setStatus("0");
        tMqDlx.setCreateTime(LocalDateTime.now());
        this.save(tMqDlx);
    }

    @Override
    public PageResult<TMqDlx> pageTMqDlx(TMqDlx tMqDlx, BasePageForm basePageForm) {
        tMqDlx.setRetryCount(null);
        IPage<TMqDlx> tMqDlxIPage = tMqDlxMapper.pageTMqDlx(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), tMqDlx);

        return new PageResult<>(tMqDlxIPage.getRecords(), tMqDlxIPage.getTotal(), tMqDlxIPage.getTotal());
    }

    @Override
    public int delete(Long id) {
        return tMqDlxMapper.wlDelete(id);
    }

    @Override
    public void batchDelete(List<Long> ids) {
        tMqDlxMapper.batchDelete(ids);
    }
}
