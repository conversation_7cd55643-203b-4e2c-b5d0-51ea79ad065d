package com.ctsi.hndxoa.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.common.BasePageForm;
import com.ctsi.hndx.common.SysBaseServiceImpl;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.PageHelperUtil;
import com.ctsi.hndx.utils.SnowflakeIdUtil;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.hndxoa.entity.TSyncAppSystemManageCompany;
import com.ctsi.hndxoa.entity.dto.TSyncAppSystemManageCompany.TSyncAppSystemAuthorizationHistoryDTO;
import com.ctsi.hndxoa.entity.dto.TSyncAppSystemManageCompany.TSyncAppSystemManageCompanyDTO;
import com.ctsi.hndxoa.entity.dto.TSyncAppSystemManageCompany.TSyncAppSystemManageCompanyPageDTO;
import com.ctsi.hndxoa.mapper.TSyncAppSystemManageCompanyMapper;
import com.ctsi.hndxoa.mapper.TSyncAppSystemManageMapper;
import com.ctsi.hndxoa.service.ITSyncAppModeratorManageService;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageCompanyService;
import com.ctsi.hndxoa.service.ITSyncAppSystemManageService;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgDTO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.model.PageResult;
import com.ctsi.ssdc.security.CscpUserDetail;
import com.ctsi.ssdc.security.SecurityUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
@SuppressWarnings("all")
public class TSyncAppSystemManageCompanyServiceImpl extends SysBaseServiceImpl<TSyncAppSystemManageCompanyMapper, TSyncAppSystemManageCompany>
        implements ITSyncAppSystemManageCompanyService {

    @Autowired
    private ITSyncAppSystemManageService itSyncAppSystemManageService;
    @Autowired
    private TSyncAppSystemManageMapper tSyncAppSystemManageMapper;
    @Autowired
    private CscpOrgRepository cscpOrgRepository;
    @Autowired
    private CscpUserRepository cscpUserRepository;
    @Autowired
    private CscpUserOrgRepository cscpUserOrgRepository;
    @Autowired
    private TSyncAppSystemManageCompanyMapper tSyncAppSystemManageCompanyMapper;
    @Autowired
    private ITSyncAppModeratorManageService itSyncAppModeratorManageService;

    private static final Integer ORG_UNIT_TYPE = 2;
    private static final Integer ORG_REGION_TYPE = 1;

    @Override
    @SuppressWarnings("unchecked")
    public PageResult<TSyncAppSystemManageCompanyPageDTO> getListByParam(
            Long orgId,
            TSyncAppSystemManageCompanyPageDTO record, BasePageForm basePageForm) {

        // 查询被授权的版主应用id
        List<Long> appIdList = itSyncAppModeratorManageService.getCurrentModeratorManageAppListByCompany(SecurityUtils.getCurrentUserId());
        // 查询应用列表
        LambdaQueryWrapper<TSyncAppSystemManage> manageLqw = Wrappers.lambdaQuery();
        manageLqw.eq(TSyncAppSystemManage::getStatus, 1);
        manageLqw.like(StringUtils.isNotEmpty(record.getAppName()),
                TSyncAppSystemManage::getAppName, record.getAppName());
        manageLqw.like(StringUtils.isNotEmpty(record.getAppCode()),
                TSyncAppSystemManage::getAppCode, record.getAppCode());
        manageLqw.and(wrapper -> wrapper
                .in(CollUtil.isNotEmpty(appIdList), TSyncAppSystemManage::getId, appIdList)
                .or()
                .eq(TSyncAppSystemManage::getModeratorFlag, 0) // 只查询没有版主的应用
        );
        IPage<TSyncAppSystemManage> pageData = tSyncAppSystemManageMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), manageLqw);

        // 收集已授权的appId集合（添加空值检查）
        Set<Long> authorizedAppIds;
        if (orgId != null) {
            CscpOrg org = cscpOrgRepository.selectById(orgId);
            if (org != null && ORG_UNIT_TYPE.equals(org.getType())) {
                LambdaQueryWrapper<TSyncAppSystemManageCompany> manageCompanyLqw = Wrappers.lambdaQuery();
                manageCompanyLqw.eq(TSyncAppSystemManageCompany::getOrgId, orgId);
                List<TSyncAppSystemManageCompany> manageCompanyList = this.baseMapper.selectListNoAdd(manageCompanyLqw);

                authorizedAppIds = manageCompanyList.stream()
                        .map(TSyncAppSystemManageCompany::getAppId)
                        .filter(Objects::nonNull) // 过滤可能的空值
                        .collect(Collectors.toSet());
            } else {
                authorizedAppIds = new HashSet<>();
            }
        } else {
            authorizedAppIds = new HashSet<>();
        }

        // 转换并设置授权状态（添加日志便于调试）
        List<TSyncAppSystemManageCompanyPageDTO> pageDTOList = pageData.getRecords().stream()
                .map(item -> {
                    TSyncAppSystemManageCompanyPageDTO dto = new TSyncAppSystemManageCompanyPageDTO();
                    BeanUtils.copyProperties(item, dto);

                    Long appIdToCheck = item.getId();
                    // 设置勾选分页中包含机构授权应用
                    if (authorizedAppIds.contains(appIdToCheck)) {
                        dto.setCheck(true);
                    }
                    // 设置勾选分页中默认推送应用、且不允许取消勾选
                    if ("1".equals(item.getAutoPush())) {
                        dto.setCheck(true);
                        dto.setAllowCancel(false);
                    }
                    return dto;
                })
                .collect(Collectors.toList());

        return new PageResult<>(pageDTOList, pageData.getTotal());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(TSyncAppSystemManageCompanyDTO record) {
        // 校验参数
        this.verifySaveOrUpdate(record);
        // 获取所有应用信息
        Map<Long, TSyncAppSystemManage> manageMap = this.getAllAppSystemManage();
        // 获取入参orgIds中的单位信息
        Map<Long, CscpOrg> cscpOrgMap = this.getUnitOrgDetail(record.getOrgIds());
        List<TSyncAppSystemManageCompany> dataList = new ArrayList<>();
        // 设置授权应用信息
        record.getAppIds().forEach(appId -> {
            TSyncAppSystemManage manage = manageMap.get(appId);
            if (null == manage) {
                throw new BusinessException("授权机构不存在");
            }
            if ("1".equals(manage.getAutoPush())) {
                return;
            }
            // 设置机构信息
            cscpOrgMap.values().forEach(org -> {
                TSyncAppSystemManageCompany entity = new TSyncAppSystemManageCompany();
                entity.setAppId(manage.getId());
                entity.setAppName(manage.getAppName());
                entity.setAppCode(manage.getAppCode());
                entity.setOrgId(org.getId());
                entity.setOrgName(org.getOrgName());
                dataList.add(entity);
            });
        });

        // 每批次处理的数量
        final int batchSize = 100;

        // 用于存储实际插入的应用ID和组织ID
        Set<Long> insertedAppIds = new HashSet<>();
        Set<Long> insertedOrgIds = new HashSet<>();

        // 分批处理插入
        try {
            // 按批次处理数据
            for (int i = 0; i < dataList.size(); i += batchSize) {
                List<TSyncAppSystemManageCompany> batchList = dataList.subList(i, Math.min(i + batchSize, dataList.size()));

                // 批量查询已存在的记录
                List<TSyncAppSystemManageCompany> existingPairs = this.baseMapper.selectExistingOrgAppPairs(batchList);

                // 转换为已存在的键值对集合
                Set<String> existingKeys = existingPairs.stream()
                        .map(pair -> pair.getOrgId() + "_" + pair.getAppId())
                        .collect(Collectors.toSet());

                // 过滤出需要插入的记录
                List<TSyncAppSystemManageCompany> toInsertList = batchList.stream()
                        .filter(entity -> !existingKeys.contains(entity.getOrgId() + "_" + entity.getAppId()))
                        .collect(Collectors.toList());

                // 批量插入新记录
                if (!toInsertList.isEmpty()) {
                    CscpUserDetail cscpUserDetail = SecurityUtils.getCurrentCscpUserDetail();
                    LocalDateTime nowTime = LocalDateTime.now();
                    // 手写的xml插入方式-部分参数手动赋值
                    for (TSyncAppSystemManageCompany tSyncAppSystemManageCompany : toInsertList) {
                        tSyncAppSystemManageCompany.setId(SnowflakeIdUtil.getSnowFlakeLongId());
                        tSyncAppSystemManageCompany.setCreateBy(SecurityUtils.getCurrentUserId());
                        tSyncAppSystemManageCompany.setCreateName(SecurityUtils.getCurrentUserName());
                        tSyncAppSystemManageCompany.setCreateTime(nowTime);
                        tSyncAppSystemManageCompany.setCompanyId(null != cscpUserDetail.getCompanyId() ? cscpUserDetail.getCompanyId() : null);
                        tSyncAppSystemManageCompany.setTenantId(null != cscpUserDetail.getTenantId() ? cscpUserDetail.getTenantId() : null);
                        tSyncAppSystemManageCompany.setDepartmentId(null != cscpUserDetail.getDepartmentId() ? cscpUserDetail.getDepartmentId() : null);
                    }
                    this.baseMapper.batchInsert(toInsertList);

                    // 收集实际插入的应用ID和组织ID
                    for (TSyncAppSystemManageCompany entity : toInsertList) {
                        insertedAppIds.add(entity.getAppId());
                        insertedOrgIds.add(entity.getOrgId());
                    }
                }
            }

            // 处理应用和组织的推送逻辑
            if (!insertedAppIds.isEmpty() && !insertedOrgIds.isEmpty()) {
                // 获取实际插入的应用信息
                List<TSyncAppSystemManage> manages = insertedAppIds.stream()
                        .map(manageMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                // 获取实际插入的组织信息
                List<CscpOrg> orgs = insertedOrgIds.stream()
                        .map(cscpOrgMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.toList());

                // 调用推送方法
                if (!manages.isEmpty() && !orgs.isEmpty()) {
                    this.pushAppsToOrgs(orgs, manages);
                }
            }
        } catch (Exception e) {
            log.error("区划授权-新增失败, 异常信息: {}", e.getMessage());
            e.printStackTrace();
            throw new BusinessException("保存失败", e);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void update(TSyncAppSystemManageCompanyDTO record) {
        final int batchSize = 100;
        boolean shouldSave = true;
        // 参数校验
        if (CollectionUtil.isEmpty(record.getAppIds())) {
            throw new BusinessException("授权应用参数错误!");
        }
        if (CollectionUtil.isEmpty(record.getOrgIds())) {
            shouldSave = false;
        }
        List<Long> targetOrgIds = getTargetOrgIds(record);

        // 处理删除逻辑
        processBatchDeletion(targetOrgIds, record.getAppIds(), batchSize);

        // 保存新记录
        if (shouldSave) {
            this.save(record);
        }
    }

    /**
     * 获取要处理的组织ID列表
     */
    private List<Long> getTargetOrgIds(TSyncAppSystemManageCompanyDTO record) {
        List<Long> allOrgIds = record.getOrgIds();

        // 如果 orgIds 为空，使用当前用户的公司ID
        if (CollectionUtil.isEmpty(allOrgIds)) {
            Long orgId = SecurityUtils.getCurrentCompanyId();
            Map<Long, CscpOrg> cscpOrgMap = this.getUnitOrgDetail(Collections.singletonList(orgId));
            return new ArrayList<>(cscpOrgMap.keySet());
        }

        return allOrgIds;
    }

    /**
     * 分批处理删除操作
     */
    private void processBatchDeletion(List<Long> orgIds, List<Long> appIds, int batchSize) {
        for (int i = 0; i < orgIds.size(); i += batchSize) {
            List<Long> batchOrgIds = orgIds.subList(i, Math.min(i + batchSize, orgIds.size()));

            LambdaUpdateWrapper<TSyncAppSystemManageCompany> luw = Wrappers.lambdaUpdate(TSyncAppSystemManageCompany.class);
            luw.set(TSyncAppSystemManageCompany::getDeleted, 1);
            luw.in(TSyncAppSystemManageCompany::getOrgId, batchOrgIds);

            // 只有当 appIds 有效且不为空时添加该条件
            if (!CollectionUtil.isEmpty(appIds)) {
                luw.in(TSyncAppSystemManageCompany::getAppId, appIds);
            }

            this.baseMapper.update(null, luw);
        }
    }

    public void pushAppsToOrgs(List<CscpOrg> orgs, List<TSyncAppSystemManage> manages) {
        // 处理每个组织与应用的关系
        for (CscpOrg org : orgs) {
            // 存储原始应用代码，用于后续比较
            String originalAppCodes = org.getPushAppCode();

            // 为当前组织添加所有应用
            for (TSyncAppSystemManage manage : manages) {
                addAppCodeToOrg(org, manage.getAppCode());
            }

            // 如果应用代码有更新，则同步组织应用配置
            if (!originalAppCodes.equals(org.getPushAppCode())) {
                updateOrgAppConfiguration(org);
            }
        }
    }

    // 私有辅助方法：将应用代码添加到组织
    private void addAppCodeToOrg(CscpOrg org, String appCode) {
        // 获取组织已有的应用代码列表
        List<String> orgPushAppCodes = new ArrayList<>(Arrays.asList(org.getPushAppCode().split(",")));

        // 如果列表中不包含该应用代码，则添加
        if (!orgPushAppCodes.contains(appCode)) {
            orgPushAppCodes.add(appCode);
        }

        // 更新组织的应用代码
        org.setPushAppCode(String.join(",", orgPushAppCodes));
    }

    // 私有辅助方法：更新组织的应用配置
    private void updateOrgAppConfiguration(CscpOrg org) {
        CscpOrgDTO cscpOrgDTO = new CscpOrgDTO();
        cscpOrgDTO = BeanConvertUtils.copyProperties(org, CscpOrgDTO.class);
        itSyncAppSystemManageService.autoOrgApp(cscpOrgDTO, false);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void remove(TSyncAppSystemManageCompanyDTO record) {
        if (CollectionUtil.isEmpty(record.getIds())) {
            throw new BusinessException("取消授权参数错误");
        }
        record.getIds().forEach(id -> {
            this.baseMapper.deleteById(id);
        });
    }

    @Override
    public List<Long> getListByAppId(Long id) {
        List<Long> ids;
        if (null == id) {
            throw new BusinessException("缺省请求参数!");
        }
        // todo 这里要拿区划管理员所在的机构;
        Long orgId = SecurityUtils.getCurrentCompanyId();
        Map<Long, TSyncAppSystemManage> manageMap = this.getAllAppSystemManage();
        TSyncAppSystemManage manage = manageMap.get(id);
        if (null == manage) {
            throw new BusinessException("参数错误!");
        }
        // 如果是自动推送应用，默认授权该市州下所有单位
        if ("1".equals(manage.getAutoPush())) {
            Map<Long, CscpOrg> cscpOrgMap = this.getUnitOrgDetail(Collections.singletonList(orgId));
            ids = new ArrayList<>(cscpOrgMap.keySet());
        } else {
            // 查询appId在该市州下所有授权机构
            ids = this.baseMapper.getListByAppId(id, orgId);
        }

        return ids;
    }

    private Map<Long, TSyncAppSystemManage> getAllAppSystemManage() {
        LambdaQueryWrapper<TSyncAppSystemManage> appSystemManageLqw = Wrappers.lambdaQuery();
        // 默认一个条件防止，插件查询报错
        appSystemManageLqw.eq(TSyncAppSystemManage::getStatus, 1);
        // 获取所有应用信息
        List<TSyncAppSystemManage> tSyncAppSystemManageList = tSyncAppSystemManageMapper.selectListNoAdd(appSystemManageLqw);
        return tSyncAppSystemManageList.stream().collect(Collectors.toMap(TSyncAppSystemManage::getId, Function.identity()));
    }

    private Map<Long, CscpOrg> getUnitOrgDetail(List<Long> orgIds) {
        // 根据orgIds,获取机构下的单位
        List<CscpOrg> orgList = cscpOrgRepository.selectOrgUnitStartWithById(orgIds);
        return orgList.stream().collect(Collectors.toMap(CscpOrg::getId, Function.identity()));
    }

    private void verifySaveOrUpdate(TSyncAppSystemManageCompanyDTO record) {
        if (CollectionUtil.isEmpty(record.getAppIds())) {
            throw new BusinessException("授权应用参数错误!");
        }
        if (CollectionUtil.isEmpty(record.getOrgIds())) {
            throw new BusinessException("授权机构参数错误!");
        }
    }

    /**
     * @param record       包含appName和appCode的查询条件
     * @param basePageForm 分页参数
     * @return 分页的应用列表
     * @description 当前登录的管理员（区划或单位）已授权的应用列表。
     * 逻辑:
     * 1. 验证用户是否为区划或单位管理员。
     * 2. 获取当前管理员的机构ID。
     * 3. 查询所有“自动推送”的应用。
     * 4. 查询所有已显式授权给该机构的应用。
     * 5. 合并以上两种应用，并根据查询条件进行分页。
     * 6. 格式化返回结果，设置勾选和是否允许取消的状态。
     */
    public PageResult<TSyncAppSystemManageCompanyPageDTO> getAuthorizedAppsForCurrentUser(
            TSyncAppSystemManageCompanyPageDTO record,
            BasePageForm basePageForm) {

        // 1. 检查用户角色
        boolean isUnitAdmin = SecurityUtils.isUnitAdmin();
        boolean isRegionAdmin = SecurityUtils.isRegionAdmin();

        if (!isUnitAdmin && !isRegionAdmin) {
            return new PageResult<>(Collections.emptyList(), 0L);
        }

        // 2. 获取当前管理员的机构ID
        Long userOrgId = SecurityUtils.getCurrentCompanyId();
        if (userOrgId == null) {
            throw new BusinessException("管理员未关联任何机构");
        }

        // 获取已授权给该机构的应用ID
        LambdaQueryWrapper<TSyncAppSystemManageCompany> authorizedLqw = Wrappers.lambdaQuery();
        authorizedLqw.eq(TSyncAppSystemManageCompany::getOrgId, userOrgId);
        List<TSyncAppSystemManageCompany> authorizedApps = this.baseMapper.selectListNoAdd(authorizedLqw);
        List<Long> authAppIdList = authorizedApps.stream().map(TSyncAppSystemManageCompany::getAppId).collect(Collectors.toList());

        // 查询当前管理员的应用版主管理应用id
        List<Long> appIdList = itSyncAppModeratorManageService.getCurrentModeratorManageAppList(SecurityUtils.getCurrentUserId());
        // 合并两个列表并去重
        List<Long> allAppIdList = Stream.concat(authAppIdList.stream(), appIdList.stream()).distinct().collect(Collectors.toList());

        // 3. 获取所有自动推送的应用ID
        LambdaQueryWrapper<TSyncAppSystemManage> autoPushLqw = Wrappers.lambdaQuery();
        autoPushLqw.eq(TSyncAppSystemManage::getAutoPush, "1")
                .like(StringUtils.isNotEmpty(record.getAppName()), TSyncAppSystemManage::getAppName, record.getAppName())
                .like(StringUtils.isNotEmpty(record.getAppCode()), TSyncAppSystemManage::getAppCode, record.getAppCode())
                .and(wrapper -> wrapper
                        .in(CollUtil.isNotEmpty(appIdList), TSyncAppSystemManage::getAppId, allAppIdList)
                        .or()
                        .eq(TSyncAppSystemManage::getModeratorFlag, 0)
                )
                .eq(TSyncAppSystemManage::getStatus, 1);
        IPage<TSyncAppSystemManage> pageData = tSyncAppSystemManageMapper.selectPageNoAdd(
                PageHelperUtil.getMPlusPageByBasePage(basePageForm), autoPushLqw);

        if (CollectionUtil.isEmpty(pageData.getRecords())) {
            return new PageResult<>(Collections.emptyList(), 0);
        }

        // 6. 组装返回结果
        List<TSyncAppSystemManageCompanyPageDTO> pageDTOList = pageData.getRecords().stream()
                .map(item -> {
                    TSyncAppSystemManageCompanyPageDTO dto = new TSyncAppSystemManageCompanyPageDTO();
                    BeanUtils.copyProperties(item, dto);

                    // 所有可见应用都默认勾选
                    dto.setCheck(true);

                    // 自动推送的应用不允许取消勾选
                    if ("1".equals(item.getAutoPush())) {
                        dto.setAllowCancel(false);
                    } else {
                        dto.setAllowCancel(true);
                    }
                    return dto;
                })
                .collect(Collectors.toList());

        return new PageResult<>(pageDTOList, pageData.getTotal());
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void authorizeAppToRegions(Long appId, List<Long> regionOrgIds) {
        if (appId == null) {
            throw new BusinessException("参数错误：应用ID不能为空");
        }

        TSyncAppSystemManage app = tSyncAppSystemManageMapper.selectById(appId);
        if (app == null) {
            throw new BusinessException("应用不存在");
        }

        // 如果regionOrgIds为空，则删除该应用的所有授权
        if (CollectionUtil.isEmpty(regionOrgIds)) {
            int deleteCount = tSyncAppSystemManageCompanyMapper.delete(
                    Wrappers.lambdaQuery(TSyncAppSystemManageCompany.class)
                            .eq(TSyncAppSystemManageCompany::getAppId, appId)
            );
            log.info("应用 '{}' (ID:{}) 的所有授权已被删除，共删除 {} 个区划授权。",
                    app.getAppName(), appId, deleteCount);
            return;
        }

        List<CscpOrg> targetOrgs = cscpOrgRepository.selectBatchIds(regionOrgIds);
        if (targetOrgs.size() != regionOrgIds.size()) {
            throw new BusinessException("一个或多个目标区划机构ID无效");
        }

        // 查询该应用下的所有机构
        List<TSyncAppSystemManageCompany> existingCompanies = tSyncAppSystemManageCompanyMapper.selectList(
                Wrappers.lambdaQuery(TSyncAppSystemManageCompany.class)
                        .eq(TSyncAppSystemManageCompany::getAppId, appId)
        );

        // 获取已存在的orgId集合
        Set<Long> existingOrgIds = existingCompanies.stream()
                .map(TSyncAppSystemManageCompany::getOrgId)
                .collect(Collectors.toSet());

        // 需要删除的机构ID（在数据库中存在但不在目标列表中）
        List<Long> toDeleteOrgIds = existingOrgIds.stream()
                .filter(orgId -> !regionOrgIds.contains(orgId))
                .collect(Collectors.toList());

        // 需要新增的机构
        List<TSyncAppSystemManageCompany> dataList = new ArrayList<>();
        for (CscpOrg org : targetOrgs) {
            if (!existingOrgIds.contains(org.getId())) {
                TSyncAppSystemManageCompany entity = new TSyncAppSystemManageCompany();
                entity.setAppId(app.getId());
                entity.setAppName(app.getAppName());
                entity.setAppCode(app.getAppCode());
                entity.setOrgId(org.getId());
                entity.setOrgName(org.getOrgName());
                dataList.add(entity);
            }
        }

        // 执行删除操作
        if (CollectionUtil.isNotEmpty(toDeleteOrgIds)) {
            tSyncAppSystemManageCompanyMapper.delete(Wrappers.lambdaQuery(TSyncAppSystemManageCompany.class)
                    .eq(TSyncAppSystemManageCompany::getAppId, appId)
                    .in(TSyncAppSystemManageCompany::getOrgId, toDeleteOrgIds));
            log.info("应用 '{}' (ID:{}) 已删除 {} 个区划的授权。", app.getAppName(), appId, toDeleteOrgIds.size());
        }

        // 执行新增操作
        if (CollectionUtil.isNotEmpty(dataList)) {
            this.saveBatch(dataList);
            log.info("应用 '{}' (ID:{}) 已成功授权给 {} 个新区划。", app.getAppName(), appId, dataList.size());
        }

        // 汇总日志
        if (CollectionUtil.isEmpty(toDeleteOrgIds) && CollectionUtil.isEmpty(dataList)) {
            log.info("应用 '{}' (ID:{}) 的授权无需调整，目标区划授权状态已是最新。", app.getAppName(), appId);
        } else {
            log.info("应用 '{}' (ID:{}) 授权调整完成：新增 {} 个，删除 {} 个区划授权。",
                    app.getAppName(), appId, dataList.size(), toDeleteOrgIds.size());
        }
    }

    /**
     * @param query        查询条件，可按应用名称、机构名称等进行过滤
     * @param basePageForm 分页参数
     * @return 分页的授权历史记录
     * @description 获取授权历史记录列表，包括已取消授权的记录
     */
    @Override
    public PageResult<TSyncAppSystemAuthorizationHistoryDTO> getAuthorizationHistory(
            TSyncAppSystemManageCompany query,
            BasePageForm basePageForm) {

        LambdaQueryWrapper<TSyncAppSystemManageCompany> lqw = Wrappers.lambdaQuery();

        // 设置查询条件
        lqw.like(StringUtils.isNotEmpty(query.getAppName()), TSyncAppSystemManageCompany::getAppName, query.getAppName());
        lqw.like(StringUtils.isNotEmpty(query.getOrgName()), TSyncAppSystemManageCompany::getOrgName, query.getOrgName());
        // admin查询所有授权记录，非admin按管理员单位查授权记录
        lqw.eq(!SecurityUtils.isSystemName(),TSyncAppSystemManageCompany::getCompanyId, SecurityUtils.getCurrentCompanyId());
        // 按创建时间降序排序
        lqw.orderByDesc(TSyncAppSystemManageCompany::getCreateTime);

        // 调用自定义的mapper方法进行分页查询，以包含逻辑删除的记录
        IPage<TSyncAppSystemManageCompany> page = PageHelperUtil.getMPlusPageByBasePage(basePageForm);
        IPage<TSyncAppSystemManageCompany> pageData = tSyncAppSystemManageCompanyMapper.selectHistoryPage(page, lqw);

        // 转换为DTO
        List<TSyncAppSystemAuthorizationHistoryDTO> dtoList = pageData.getRecords().stream()
                .map(entity -> {
                    TSyncAppSystemAuthorizationHistoryDTO dto = new TSyncAppSystemAuthorizationHistoryDTO();
                    dto.setId(entity.getId());
                    dto.setAppId(entity.getAppId());
                    dto.setAppName(entity.getAppName());
                    dto.setAppCode(entity.getAppCode());
                    dto.setOrgId(entity.getOrgId());
                    dto.setOrgName(entity.getOrgName());
                    dto.setCreateTime(entity.getCreateTime());
                    dto.setCreateName(entity.getCreateName());
                    dto.setUpdateTime(entity.getUpdateTime());
                    // 转换删除状态为授权状态
                    dto.setStatus(entity.getDeleted() != null && entity.getDeleted() == 1 ? "REVOKED" : "ACTIVE");
                    return dto;
                })
                .collect(Collectors.toList());

        return new PageResult<>(dtoList, pageData.getTotal());
    }

}
