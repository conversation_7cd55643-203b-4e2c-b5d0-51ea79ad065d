package com.ctsi.hndxoa.entity.dto;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
public class SyncReceiveOrgTree {

    @ExcelProperty("组织机构主键")
    private String id;

    @ExcelProperty("上级机构")
    private String parentId;

    @ExcelProperty("机构编码")
    private String code;

    @ExcelProperty("组织机构")
    private String name;

    @ExcelProperty("机构排序")
    private String orderBy;

    @ExcelProperty("统一机构现有机构代码")
    private String isExistsOrgCode;

    @ExcelProperty("新机构名称")
    private String newImportOrgName;

    @ExcelIgnore
    private CscpOrg oldOrg;

    @ExcelIgnore
    private Integer oldMaxNumber;

    @ExcelIgnore
    private int level;

    @ExcelIgnore
    private String generateCode;

    @ExcelIgnore
    private Long generateId;

    @ExcelIgnore
    private Long generateParentId;

    @ExcelIgnore
    private String generatePathCode;

    @ExcelIgnore
    private Integer generateMaxNumber;

    @ExcelIgnore
    private Integer generateType;

    @ExcelIgnore
    private Integer generateLevel;

    @ExcelIgnore
    private String generateParentCode;

    @ExcelIgnore
    @Deprecated
    private String generateParentOrgIdPath;

    @ExcelIgnore
    @Deprecated
    private String generateParentOrgCodePath;

    @ExcelIgnore
    private String generateOrgIdPath;

    @ExcelIgnore
    private String generateOrgCodePath;

    @ExcelIgnore
    private List<SyncReceiveOrgTree> children;

    @ApiModelProperty(value = "失败原因")
    @ExcelProperty("失败原因")
    private String failedReason;

}
