package com.ctsi.hndxoa.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ctsi.hndx.enums.FileBasePathName;
import com.ctsi.hndx.exception.BusinessException;
import com.ctsi.hndx.result.ResultCode;
import com.ctsi.hndx.utils.BeanConvertUtils;
import com.ctsi.hndx.utils.StringUtils;
import com.ctsi.hndxoa.entity.dto.SyncReceiveUserDTO;
import com.ctsi.hndxoa.service.ISyncReceiveUserService;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.CscpUser;
import com.ctsi.ssdc.admin.domain.CscpUserOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpOrgNameIdListDTO;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.admin.repository.CscpOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserOrgRepository;
import com.ctsi.ssdc.admin.repository.CscpUserRepository;
import com.ctsi.ssdc.admin.service.CscpUserOrgService;
import com.ctsi.ssdc.admin.service.CscpUserService;
import com.ctsi.sysimport.domain.dto.TSysImportDTO;
import com.ctsi.sysimport.service.ISysImportService;
import com.ctsi.sysimport.util.SysImportTypeUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContext;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * 用户同步服务实现类
 * <AUTHOR>
 */
@Slf4j
@Service
public class SyncReceiveReceiveUserServiceImpl implements ISyncReceiveUserService {

    final String SUCCESS_MESSAGE = "同步成功，记录导入结果管理";
    final String FAILURE_MESSAGE = "存在同步失败数据，记录导入结果管理并上传Excel失败文件";

    final Pattern ID_CARD_PATTERN = Pattern.compile("^\\d{17}[0-9X]$");

    @Autowired
    private CscpUserService cscpUserService;

    @Autowired
    private CscpUserRepository cscpUserRepository;

    @Autowired
    private CscpUserOrgRepository cscpUserOrgRepository;

    @Autowired
    private CscpUserOrgService cscpUserOrgService;

    @Autowired
    private CscpOrgRepository cscpOrgRepository;

    @Autowired
    private ISysImportService iSysImportService;

    /**
     * 批量保存用户
     *
     * @param dataList 用户数据列表
     * @return 保存结果
     */
    @Override
    public String saveBatchUser(List<SyncReceiveUserDTO> dataList, String source) {
        log.info("同步用户数据开始-excel数据量：{}", dataList.size());

        Map<String, SyncReceiveUserDTO> dataMap = new HashMap<>();
        for (SyncReceiveUserDTO syncReceiveUserDTO : dataList) {
            dataMap.putIfAbsent(syncReceiveUserDTO.getId(), syncReceiveUserDTO);
        }
        for (SyncReceiveUserDTO syncReceiveUserDTO : dataList) {
            syncReceiveUserDTO.setRealName(syncReceiveUserDTO.getRealName().replaceAll(" ",""));
            SyncReceiveUserDTO userDTO = dataMap.get(syncReceiveUserDTO.getId());
            if (null != userDTO) {
                String oldFlag = userDTO.getOrgIds() + userDTO.getOrderBy();
                String newFlag = syncReceiveUserDTO.getOrgIds() + syncReceiveUserDTO.getOrderBy();
                if (!oldFlag.equals(newFlag)) {
                    String strOrgId = syncReceiveUserDTO.getOrgIds();
                    userDTO.setOrgIds(userDTO.getOrgIds() + "," + strOrgId);

                    String strOrderBy = syncReceiveUserDTO.getOrderBy();
                    userDTO.setOrderBy(userDTO.getOrderBy() + "," + strOrderBy);
                }
            }
        }

        dataList = new LinkedList<>(dataMap.values());

        // 基于SyncReceiveUserDTO的id对数据去重
        dataList = dataList.stream().collect(
                Collectors.collectingAndThen(
                        Collectors.toCollection(() -> new TreeSet<>(Comparator.comparing(SyncReceiveUserDTO::getId))),
                        ArrayList::new
                )
        );
        log.info("同步用户数据-实际数据量：{}", dataList.size());
        List<SyncReceiveUserDTO> failedList =  Collections.synchronizedList(new ArrayList<>());
        // 数据校验
        this.checkUserRequestData(dataList);
        if ("isImport".equals(source)) {
            // 获取当前SecurityContext
            SecurityContext securityContext = SecurityContextHolder.getContext();
            SyncReceiveReceiveUserServiceImpl service = (SyncReceiveReceiveUserServiceImpl) AopContext.currentProxy();

            SecurityContextHolder.setContext(securityContext);

            // 使用线程池管理线程
            ExecutorService executorService = Executors.newFixedThreadPool(30);
            List<Future<?>> futures = new ArrayList<>();

            // 将 dataList 均分到 100 个线程
            int batchSize = (dataList.size() + 99) / 100;
            for (int i = 0; i < dataList.size(); i += batchSize) {
                int end = Math.min(i + batchSize, dataList.size());
                List<SyncReceiveUserDTO> batch = dataList.subList(i, end);

                futures.add(executorService.submit(() -> {
                    SecurityContextHolder.setContext(securityContext);
                    for (SyncReceiveUserDTO data : batch) {
                        try {
                            // 使用AopContext.currentProxy()获取代理对象，实现自我调用避免@Transactional失效
                            service.syncUser(data);
                        } catch (Exception e) {
                            // 记录同步失败的日志
                            log.error("同步用户{}失败, 失败原因:{}", data.getLoginName(), e.getMessage(), e);
                            data.setFailedReason(e.toString());
                            failedList.add(data);
                        }
                    }
                }));
            }

            for (Future<?> future : futures) {
                try {
                    future.get();
                } catch (InterruptedException | ExecutionException e) {
                    log.error("任务执行失败: {}", e.getMessage(), e);
                }
            }

            // 关闭线程池
            executorService.shutdown();

            TSysImportDTO sysOrgImportDTO = new TSysImportDTO();
            sysOrgImportDTO.setTotalNo(dataList.size());
            sysOrgImportDTO.setFailedNo(failedList.size());
            sysOrgImportDTO.setSuccessNo(dataList.size() - failedList.size());
            // 导入数据类型
            sysOrgImportDTO.setType(SysImportTypeUtils.getImportType(FileBasePathName.CHANG_DE_USER_IMPORT));

            log.info("同步用户数据结束-失败数据量：{}", failedList.size());
            // 如果没有失败记录，则直接保存
            if (CollectionUtils.isEmpty(failedList)) {
                iSysImportService.create(sysOrgImportDTO);
            } else {
                // 保存导入记录，并上传Excel失败文件
                iSysImportService.saveAndUploadFile(sysOrgImportDTO, failedList, SyncReceiveUserDTO.class, FileBasePathName.CHANG_DE_USER_IMPORT);
            }
            return !failedList.isEmpty() ? FAILURE_MESSAGE :SUCCESS_MESSAGE ;
        } else {
            for (SyncReceiveUserDTO data : dataList) {
                try {
                    // 使用AopContext.currentProxy()获取代理对象，实现自我调用避免@Transactional失效
                    SyncReceiveReceiveUserServiceImpl service = (SyncReceiveReceiveUserServiceImpl) AopContext.currentProxy();
                    service.syncUser(data);
                } catch (Exception e) {
                    log.error("同步用户{}失败, 失败原因:{}", data.getLoginName(), e.getMessage(), e);
                }
            }
            return "同步操作结束";
        }
    }

    /**
     * 同步单个用户
     *
     * @param data 用户数据
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void syncUser(SyncReceiveUserDTO data) {
        // 查询用户是否存在
        LambdaQueryWrapper<CscpUser> userLqw = Wrappers.lambdaQuery();
        userLqw.eq(CscpUser::getStrId, data.getId())
                .eq(CscpUser::getUserOrigin, "常德");
        CscpUser cscpUser = cscpUserRepository.selectOneNoAdd(userLqw);

        List<String> externalIds = Arrays.asList(data.getOrgIds().split(","));
        LambdaQueryWrapper<CscpOrg> orgLqw = Wrappers.lambdaQuery();
        orgLqw.in(CscpOrg::getExternalId, externalIds);
        List<CscpOrg> cscpOrgList = cscpOrgRepository.selectListNoAdd(orgLqw);
        if (CollectionUtils.isEmpty(cscpOrgList)) {
            throw new BusinessException("用户:{} ,关联机构数据错误!", data.getRealName());
        }
        if (StringUtils.isNotEmpty(data.getIdCardNo())) {
            String idCardNo = data.getIdCardNo();
            if (idCardNo == null ||!ID_CARD_PATTERN.matcher(idCardNo).matches()) {
                throw new BusinessException("身份证号码格式不正确，请输入 18 位数字或前 17 位数字加最后一位大写 X");
            }
        }
        List<Long> orgIds = cscpOrgList.stream().map(CscpOrg::getId).collect(Collectors.toList());
        List<String> sorts = Arrays.asList(data.getOrderBy().split(","));
        List<CscpOrgNameIdListDTO> orgNameIds = new ArrayList<>();
        for (int i = 0; i < orgIds.size(); i++) {
            CscpOrgNameIdListDTO cscpOrgNameIdListDTO = new CscpOrgNameIdListDTO();
            cscpOrgNameIdListDTO.setId(orgIds.get(i));
            cscpOrgNameIdListDTO.setUserOrgSort(Integer.valueOf(sorts.get(i)));
            orgNameIds.add(cscpOrgNameIdListDTO);
        }
        CscpUserDTO cscpUserDTO;
        if (cscpUser == null) {
            // 用户不存在，进行插入操作
            cscpUserDTO = BeanConvertUtils.copyProperties(data, CscpUserDTO.class);
            if (cscpUserDTO == null) {
                throw new BusinessException(ResultCode.PARAM_IS_INVALID);
            }
            // 设置一些默认值
            cscpUserDTO.setUserOrigin("常德");
            if (cscpUserDTO.getStatus() == null) {
                cscpUserDTO.setStatus(1);
            }
            cscpUserDTO.setStrId(data.getId());
            cscpUserDTO.setExamineStatus(1);
            cscpUserDTO.setSex(StringUtils.isNotEmpty(data.getSex()) ? "女".equals(data.getSex()) ? 1:0 : null);
            cscpUserDTO.setOrgIdList(orgIds);
            cscpUserDTO.setOrgNameList(orgNameIds);
            if (orgIds.size() > 1) {
                cscpUserDTO.setDefaultDepart(orgIds.get(1));
            }

            cscpUserService.insert(cscpUserDTO);
        } else {
            // 用户存在，进行更新操作
            cscpUserDTO = BeanConvertUtils.copyProperties(cscpUser, CscpUserDTO.class);
            cscpUserDTO.setRealName(data.getRealName());
            cscpUserDTO.setMobile(data.getMobile());
            cscpUserDTO.setSex(StringUtils.isNotEmpty(data.getSex()) ? "女".equals(data.getSex()) ? 1:0 : null);
            cscpUserDTO.setOrgIdList(orgIds);
            cscpUserDTO.setOrgNameList(orgNameIds);
            if (orgIds.size() > 1) {
                cscpUserDTO.setDefaultDepart(orgIds.get(1));
            }
            cscpUserService.update(cscpUserDTO);
        }
        // 同步用户与机构的关系
//        syncUserOfOrg(data, cscpUser);
    }

    /**
     * 同步用户与机构的关系
     *
     * @param data     用户数据
     * @param cscpUser 用户实体
     */
    private void syncUserOfOrg(SyncReceiveUserDTO data, CscpUser cscpUser) {
        if (StringUtils.isEmpty(data.getOrgIds())) {
            // 清除关系
            LambdaQueryWrapper<CscpUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
            userOrgLqw.eq(CscpUserOrg::getUserId, cscpUser.getId());
            cscpUserOrgRepository.delete(userOrgLqw);
            return;
        }

        // 查询机构并校验
        LambdaQueryWrapper<CscpOrg> orgLqw = new LambdaQueryWrapper<>();
        orgLqw.in(CscpOrg::getExternalId, Arrays.asList(data.getOrgIds().split(",")));
        List<CscpOrg> cscpOrgList = cscpOrgRepository.selectListNoAdd(orgLqw);

        if (CollectionUtils.isEmpty(cscpOrgList)) {
            throw new BusinessException("用户所属机构不存在! 用户ID: {}, 机构ID列表: {}", cscpUser.getId(), data.getOrgIds());
        }

        Set<String> missingOrgIds = new HashSet<>(Arrays.asList(data.getOrgIds().split(",")));
        missingOrgIds.removeAll(cscpOrgList.stream().map(CscpOrg::getExternalId).collect(Collectors.toSet()));
        if (!missingOrgIds.isEmpty()) {
            throw new BusinessException("用户所属机构不存在! 缺失的机构ID列表: {}", missingOrgIds);
        }

        // 查询现有用户机构关系
        LambdaQueryWrapper<CscpUserOrg> userOrgLqw = new LambdaQueryWrapper<>();
        userOrgLqw.eq(CscpUserOrg::getUserId, cscpUser.getId());
        List<CscpUserOrg> existingUserOrgs = cscpUserOrgRepository.selectListNoAdd(userOrgLqw);

        Set<Long> existingOrgIds = existingUserOrgs.stream().map(CscpUserOrg::getOrgId).collect(Collectors.toSet());
        Set<Long> newOrgIds = cscpOrgList.stream().map(CscpOrg::getId).collect(Collectors.toSet());

        // 计算新增和删除的机构ID
        Set<Long> addIds = new HashSet<>(newOrgIds);
        addIds.removeAll(existingOrgIds);

        Set<Long> removeIds = new HashSet<>(existingOrgIds);
        removeIds.removeAll(newOrgIds);

        // 批量新增机构关系
        if (!addIds.isEmpty()) {
            List<CscpUserOrg> addUserOrgs = addIds.stream()
                    .map(id -> {
                        CscpUserOrg userOrg = new CscpUserOrg();
                        userOrg.setUserId(cscpUser.getId());
                        userOrg.setOrgId(id);
                        userOrg.setDeleted(0);
                        return userOrg;
                    })
                    .collect(Collectors.toList());
            cscpUserOrgService.saveBatch(addUserOrgs);
        }

        // 批量删除机构关系
        if (!removeIds.isEmpty()) {
            List<Long> removeUserOrgIds = existingUserOrgs.stream()
                    .filter(userOrg -> removeIds.contains(userOrg.getOrgId()))
                    .map(CscpUserOrg::getId)
                    .collect(Collectors.toList());
            cscpUserOrgService.removeByIds(removeUserOrgIds);
        }
    }

    /**
     * 检查用户请求数据
     *
     * @param dataList 用户数据列表
     */
    private void checkUserRequestData(List<SyncReceiveUserDTO> dataList) {
        if (CollectionUtils.isEmpty(dataList)) {
            throw new BusinessException("用户数据列表不能为空!");
        }

        dataList.forEach(data -> {
            if (StringUtils.isEmpty(data.getId())) {
                throw new BusinessException("用户id不能为空值! 用户登录名: {}", data.getLoginName());
            }
            if (StringUtils.isEmpty(data.getLoginName())) {
                throw new BusinessException("用户登录名不能为空值! 用户ID: {}", data.getId());
            }
            if (StringUtils.isEmpty(data.getRealName())) {
                throw new BusinessException("用户真实姓名不能为空值! 用户ID: {}", data.getId());
            }
            if (StringUtils.isEmpty(data.getOrgIds())) {
                throw new BusinessException("用户组织机构不能为空值! 用户ID: {}", data.getId());
            }
        });
    }
}
