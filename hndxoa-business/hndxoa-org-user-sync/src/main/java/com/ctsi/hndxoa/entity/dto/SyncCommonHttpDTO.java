package com.ctsi.hndxoa.entity.dto;

import com.ctsi.hndxoa.entity.TSyncAppSystemManage;
import com.ctsi.ssdc.admin.domain.CscpOrg;
import com.ctsi.ssdc.admin.domain.dto.CscpUserDTO;
import com.ctsi.ssdc.security.CscpUserDetail;
import lombok.Data;

import java.util.Map;

@Data
public class SyncCommonHttpDTO {

    private TSyncAppSystemManage appSystemManage;

    private Map<String, Object> bodyParams;

    private CscpUserDTO cscpUserDto;

    private CscpOrg cscpOrg;

    private SyncOrgUserDTO syncOrgUserDTO;

    private CscpUserDetail cscpUserDetail;

    private String uuid;

}
